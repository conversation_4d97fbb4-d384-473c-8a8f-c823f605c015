<template>
  <div class="app-container">
    <panel-group :data="dashboardData" @handleSetLineChartData="handleSetLineChartData" />
    <el-row style="background: #fff; padding: 16px 16px 0; margin-bottom: 10px">
      <x-radio
        style="margin-bottom: 10px; padding-left: 10px"
        size="small"
        button-style="solid"
        v-model="queryParams.timeSpanType"
        button
        url="/options/GetTimeSpanTypes"
        @change="handleSetLineChartData()"
      ></x-radio>
      <line-chart
        :chart-data="dashboardLineData"
        :legentShow="true"
        :titleShow="true"
        height="300px"
        :titleText="dashboardLineData.title"
        :interval="dashboardLineData.interval"
      />
    </el-row>
    <panel-table :tableData="dashboardTableData" />
  </div>
</template>

<script>
import PanelGroup from './components/PanelGroup'
import PanelTable from './components/PanelTable'
import LineChart from '@/components/EChatrs/Line/LineChart'
import { dashboardApi, analysisApi } from '@/api'
export default {
  name: 'DashboardAdmin',
  components: {
    PanelGroup,
    LineChart,
    PanelTable,
  },
  data() {
    return {
      queryParams: {
        appFlag: -1,
        channelId: -1,
        analysisType: 0,
        timeSpanType: 5,
      },
      dashboardData: {},
      dashboardLineData: {},
      dashboardTableData: [],
    }
  },
  async created() {
    this.$xloading.show()
    await this.getDatas()
    await this.getLineDatas(0)
    await this.getChannelStats()
    this.$xloading.hide()
  },
  methods: {
    async getDatas() {
      var res = await dashboardApi.getDashboard()
      if (res.code == 0) {
        this.dashboardData = res.data || {}
      }
    },
    async getChannelStats() {
      var res = await dashboardApi.getChanelStats()
      if (res.code == 0) {
        this.dashboardTableData = res.data || {}
      }
    },
    async getLineDatas(type) {
      var params = Object.assign({}, this.queryParams)
      params.analysisType = type
      var res = await analysisApi.getinstalllinedata(params)
      if (res.code == 0) {
        this.dashboardLineData = res.data || {}
      }
    },
    async handleSetLineChartData(type) {
      this.$xloading.show()
      await this.getLineDatas(type)
      this.$xloading.hide()
    },
  },
}
</script>
