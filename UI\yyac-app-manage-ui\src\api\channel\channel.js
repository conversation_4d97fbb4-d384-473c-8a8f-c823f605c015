import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/channel/getList',
    method: 'get',
    params,
  })
}

export function getDetail(id) {
  return request({
    url: '/channel/getDetail',
    method: 'get',
    params: {
      id,
    },
  })
}

export function addChannel(data) {
  return request({
    url: '/channel/add',
    method: 'post',
    data: data,
  })
}

export function editChannel(data) {
  return request({
    url: '/channel/edit',
    method: 'post',
    data: data,
  })
}

export function delChannel(id) {
  return request({
    url: '/channel/delete',
    method: 'post',
    data: {
      id,
    },
  })
}

export function getOptions(params) {
  return request({
    url: '/channel/options',
    method: 'get',
    params,
  })
}

export function downloadApk(data) {
  return request({
    url: '/channel/downloadApk',
    method: 'post',
    data,
  })
}

export function batchEditChannel(data) {
  return request({
    url: '/channel/batchEdit',
    method: 'post',
    data: data,
  })
}
