(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-43e40a62","chunk-4c73d0be","chunk-3688f091","chunk-2d0bdf53"],{"00d8c":function(e,t,a){"use strict";a("6776")},"045c":function(e,t,a){},"0a062":function(e,t,a){"use strict";a("045c")},"0e2a":function(e,t,a){},1223:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:"批量修改渠道",visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[a("div",{staticClass:"batch-edit-content"},[a("div",{staticClass:"selected-info"},[a("h4",[e._v("已选择 "+e._s(e.selectedChannels.length)+" 个渠道：")]),e._v(" "),a("div",{staticClass:"selected-list"},e._l(e.selectedChannels,(function(t){return a("el-tag",{key:t.id,staticStyle:{margin:"2px"},attrs:{size:"small"}},[e._v("\n          "+e._s(t.appName)+" - "+e._s(t.name)+"\n        ")])})),1)]),e._v(" "),a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[a("el-form-item",{attrs:{label:"所属项目",prop:"appId"}},[a("x-select",{attrs:{customRender:"",url:"/appKeyConfig/options"},model:{value:e.form.appId,callback:function(t){e.$set(e.form,"appId",t)},expression:"form.appId"}})],1)],1)],1)])},n=[],i=(a("96cf"),a("1da1")),s=a("365c"),l={data:function(){return{visible:!1,loading:!1,selectedChannels:[],form:{appId:""},rules:{appId:[{required:!0,message:"请选择所属项目",trigger:"change"}]}}},methods:{open:function(e){this.selectedChannels=e,this.reset(),this.visible=!0},close:function(){this.reset(),this.visible=!1},reset:function(){this.form={appId:""},this.$xResetForm("form")},submitForm:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$refs["form"].validate(function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(a){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(a)try{r=t.selectedChannels.map((function(e){return e.id})),t.$confirm("是否确认要批量修改所属项目？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$xloading.show(),e.next=3,s["f"].batchEditChannel({ids:r,appId:t.form.appId});case 3:a=e.sent,t.$xloading.hide(),0==a.code?(t.$xMsgSuccess("批量修改成功"),t.close(),t.$emit("ok")):t.$xMsgError("批量修改失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(e){t.$xloading.hide(),t.$xMsgError("批量修改失败！"+e.message)}))}catch(n){t.$xloading.hide()}case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}());case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},o=l,c=(a("260c"),a("2877")),u=Object(c["a"])(o,r,n,!1,null,"3853a2ee",null);t["default"]=u.exports},"13f9":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"1000px"},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入通知标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"发布时间",prop:"createOn"}},[a("el-date-picker",{attrs:{placeholder:"请选择发布时间",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.createOn,callback:function(t){e.$set(e.form,"createOn",t)},expression:"form.createOn"}}),e._v(" "),a("span",{staticClass:"extra line"},[e._v("发布时间非必填，不填则默认当前时间")])],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"是否置顶",prop:"isTop"}},[a("el-input-number",{attrs:{"controls-position":"right"},model:{value:e.form.isTop,callback:function(t){e.$set(e.form,"isTop",t)},expression:"form.isTop"}}),e._v(" "),a("span",{staticClass:"extra line"},[e._v("数值越大，排名置顶越前")])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"内容",prop:"content"}},[a("tinymce",{attrs:{"upload-type":e.uploadType},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1)],1)],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("editor",{staticClass:"editor",attrs:{id:"tinymce",init:e.editorInit},model:{value:e.content,callback:function(t){e.content=t},expression:"content"}})},l=[],o=(a("c5f6"),a("e562"),a("9917")),c=(a("0d68"),a("46c3"),a("64d8"),a("4237"),a("d2dc"),a("84ec8"),a("07d1"),a("4ea8"),a("c3d7"),a("4bd0"),a("0a9d"),["code undo redo | cut copy paste pastetext | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent |","styleselect formatselect fontselect fontsizeselect | bullist numlist | blockquote subscript superscript removeformat |","table image charmap hr insertdatetime | preview "]),u=c,d=["code","link","anchor","lists","table","image","charmap","insertdatetime","preview"],p=d,m=a("3f5e"),f=a("5f87"),h={components:{Editor:o["a"]},props:{value:{type:String},plugins:{type:[String,Array],default:function(){return p}},toolbar:{type:[String,Array],default:function(){return u}},uploadType:{type:Number}},mounted:function(){},data:function(){var e=this;return{content:this.value,editorInit:{skin_url:"/static/tinymce/skins/ui/oxide",content_css:"/static/tinymce/skins/content/default/content.css",language_url:"/static/tinymce/langs/zh_CN.js",language:"zh_CN",height:500,branding:!1,menubar:!1,toolbar:this.toolbar,plugins:this.plugins,images_upload_url:m["uploadUrl"],image_uploadtab:!0,images_upload_handler:function(t,a,r){var n,i;n=new XMLHttpRequest,n.withCredentials=!1,n.open("POST",m["uploadUrl"]),n.setRequestHeader("Access-Token",Object(f["c"])()),n.onload=function(){var e;200==n.status?(e=JSON.parse(n.responseText),e&&0==e.code?a(e.data.url):r("上传失败: "+n.responseText)):r("HTTP Error: "+n.status)},i=new FormData,i.append("uploadType",e.uploadType),i.append("file",t.blob(),t.filename()),n.send(i)}}}},watch:{value:function(e){this.content=e},content:function(e){this.$emit("input",e)}}},g=h,v=a("2877"),b=Object(v["a"])(g,s,l,!1,null,null,null),y=b.exports,w=a("365c"),x=a("60fe"),_={components:{Tinymce:y},data:function(){return{title:"",visible:!1,loading:!1,form:{},rules:{title:[{required:!0,message:"请输入标题",trigger:"blur"}],content:[{required:!0,message:"请输入内容",trigger:"blur"}]},uploadType:x["b"].appSystemNotice}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,w["r"].getDetail(t.id);case 6:a=e.sent,0==a.code&&(this.form=a.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.visible=!1},reset:function(){this.form={id:void 0,title:void 0,content:""},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r,n,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=17;break}if(e.$xloading.show(),r=Object.assign({},e.form),n=r.id,void 0==n){t.next=12;break}return t.next=7,w["r"].editSystemNotice(r);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,w["r"].addSystemNotice(r);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},k=_,$=Object(v["a"])(k,r,n,!1,null,null,null);t["default"]=$.exports},1693:function(e,t,a){},1717:function(e,t,a){"use strict";a("1693")},"1ba4":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"锚点标识"}},[a("el-input",{model:{value:e.queryParams.symbol,callback:function(t){e.$set(e.queryParams,"symbol",t)},expression:"queryParams.symbol"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"锚点名称"}},[a("el-input",{model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["symbolConfig:add"],expression:"['symbolConfig:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center","min-width":"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"symbol",label:"锚点标记",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"锚点名称",align:"center","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"type",label:"值类型",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.type?a("div",{staticClass:"green"},[e._v("Decimal")]):e._e(),e._v(" "),2==t.row.type?a("div",{staticClass:"coral"},[e._v("String")]):e._e(),e._v(" "),3==t.row.type?a("div",{staticClass:"red"},[e._v("Bool")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"dest",label:"描述",align:"center","min-width":"250"}}),e._v(" "),a("el-table-column",{attrs:{prop:"addTime",label:"添加时间",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"editTime",label:"修改时间",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["symbolConfig:edit"],expression:"['symbolConfig:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(a){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["symbolConfig:delete"],expression:"['symbolConfig:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("4624"),l=a("365c"),o=a("ec0e"),c={components:{EditDialog:o["default"]},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["o"].getList(t);case 4:a=e.sent,this.tableData=a,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,l["o"].delsymbolConfig(t.id);case 3:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("删除成功")):a.$xMsgError("删除失败！"+r.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},u=c,d=a("2877"),p=Object(d["a"])(u,r,n,!1,null,null,null);t["default"]=p.exports},"1ccc":function(e,t,a){},"1cfa":function(e,t,a){"use strict";a("8895")},"1e4b":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row")],1)},n=[],i=(a("8e6e"),a("ac6a"),a("456d"),a("ade3")),s=a("2f62");function l(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function o(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?l(Object(a),!0).forEach((function(t){Object(i["a"])(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):l(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var c={name:"Dashboard",data:function(){return{cardCol:{xs:{span:24},sm:{span:12},md:{span:8}},quickStartCol:{xs:{span:12},sm:{span:8},xl:{span:6}}}},computed:o({},Object(s["c"])(["name","minaType"])),methods:{quickStart:function(e){e.href&&this.$router.push({path:e.href})}}},u=c,d=(a("0a062"),a("2877")),p=Object(d["a"])(u,r,n,!1,null,"9a65b0bc",null);t["default"]=p.exports},"1f34":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:add"],expression:"['system:user:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:add"],expression:"['system:user:add']"}],attrs:{size:"mini",type:"danger",icon:"el-icon-plus"},on:{click:e.handleDeleteAllUserLoginToken}},[e._v("清除所有登录用户Token")])],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"id",label:"ID",align:"center",width:"80"}}),e._v(" "),a("el-table-column",{attrs:{prop:"userName",label:"登录账号",align:"center",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"nickName",label:"姓名",align:"center",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"authUId",label:"授权中心ID",align:"center",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status",label:"状态",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{disabled:!e.changeStatusPermission,"active-value":!0,"inactive-value":!1,"active-color":"#13ce66"},on:{change:function(a){return e.onStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"loginIp",label:"最后登录IP",align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(e._s(t.row.loginIp))]),e._v(" "),a("div",[e._v(e._s(t.row.loginIpLocation))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"loginTime",label:"最后登录时间",align:"center",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createOn",label:"创建时间",align:"center",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding",width:"250"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:resetPassword"],expression:"['system:user:resetPassword']"}],attrs:{size:"mini"},on:{click:function(a){return e.handleResetPassword(t.row)}}},[e._v("重置密码")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:edit"],expression:"['system:user:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(a){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:user:delete"],expression:"['system:user:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("4624"),l=a("365c"),o=a("bb53"),c={mixins:[s["a"]],components:{EditDialog:o["default"]},data:function(){return{queryParams:{},loading:!1,tableData:{},changeStatusPermission:!1}},mounted:function(){var e=this.$hasPermission("system:user:changeStatus");this.changeStatusPermission=e},methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["q"].getList(t);case 4:a=e.sent,this.tableData=a,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,l["q"].deleteUser(t.id);case 3:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("删除成功")):a.$xMsgError("删除失败！"+r.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleDeleteAllUserLoginToken:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除清除用户登录Token？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.$xloading.show(),e.next=3,l["q"].deleteAllUserLoginToken();case 3:a=e.sent,t.$xloading.hide(),0==a.code?(t.$refs.table.refresh(),t.$xMsgSuccess("删除成功")):t.$xMsgError("删除失败！"+a.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){t.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleResetPassword:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$xloading.show(),this.$confirm("是否确认重置该用户的密码","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l["q"].resetPassword(t.id);case 2:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("重置密码成功")):a.$xMsgError("操作失败！"+r.msg);case 5:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 2:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},onStatusChange:function(e){var t=this,a=1==e.status?"启用":"禁用";this.$confirm("是否确认【".concat(a,"】该账号？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.$xloading.show(),a.next=3,l["q"].changeStatus(e.id,e.status);case 3:r=a.sent,t.$xloading.hide(),0==r.code?t.$xMsgSuccess("操作成功"):t.$xMsgError("操作失败！"+r.msg),t.$refs.table.refresh();case 7:case"end":return a.stop()}}),a)})))).catch((function(){t.$xloading.hide(),t.$refs.table.refresh()}))}}},u=c,d=a("2877"),p=Object(d["a"])(u,r,n,!1,null,null,null);t["default"]=p.exports},2:function(e,t){},"202d":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{"label-width":"100px",inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"标题"}},[a("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"发布人"}},[a("x-select",{attrs:{url:"/system/user/options"},model:{value:e.queryParams.publisher,callback:function(t){e.$set(e.queryParams,"publisher",t)},expression:"queryParams.publisher"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"发布时间"}},[a("el-date-picker",{attrs:{type:"daterange","value-format":"yyyy-MM-dd","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.createOnTimePickerOptions},model:{value:e.queryParams.createOn,callback:function(t){e.$set(e.queryParams,"createOn",t)},expression:"queryParams.createOn"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["systemNotice:add"],expression:"['systemNotice:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight,"default-sort":{prop:"isTop",order:"descending"}},on:{"sort-change":e.handleSortChange}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"title",label:"标题",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"isTop",label:"是否置顶",align:"center",width:"130",sortable:"custom"},scopedSlots:e._u([{key:"header",fn:function(){return[a("x-table-header-tip",{attrs:{label:"是否置顶",tip:"数值越大，排名置顶越前"}})]},proxy:!0}])}),e._v(" "),a("el-table-column",{attrs:{prop:"publisherName",label:"发布管理员",align:"center",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createOn",label:"发布时间",align:"center",width:"180",sortable:"custom"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["systemNotice:edit"],expression:"['systemNotice:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(a){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["systemNotice:del"],expression:"['systemNotice:del']"}],attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},n=[],i=(a("96cf"),a("1da1")),s=(a("6b54"),a("365c"));a("60fe");var l=864e5,o={shortcuts:[{text:"昨日",onClick:function(e){var t=new Date,a=new Date;t.setTime(t.getTime()-l),a=t,e.$emit("pick",[t,a])}},{text:"今日",onClick:function(e){var t=new Date,a=new Date;e.$emit("pick",[t,a])}},{text:"最近三日",onClick:function(e){var t=new Date,a=new Date;t.setTime(t.getTime()-2*l),e.$emit("pick",[t,a])}},{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;t.setTime(t.getTime()-6*l),e.$emit("pick",[t,a])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;t.setTime(t.getTime()-29*l),e.$emit("pick",[t,a])}}]},c=a("4624"),u=a("13f9"),d={components:{EditDialog:u["default"]},mixins:[c["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{},createOnTimePickerOptions:o}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),t.createOn&&(t.beginTime=t.createOn[0],t.endTime=t.createOn[1],delete t.createOn),e.next=5,s["r"].getList(t);case 5:a=e.sent,this.tableData=a,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,s["r"].delSystemNotice(t.id);case 3:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("删除成功")):a.$xMsgError("删除失败！"+r.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleSortChange:function(e){var t=e.order,a=e.prop;a&&(this.queryParams.field=a,this.queryParams.order="ascending"==t?"asc":"desc",this.$refs.table.refresh(!0))}}},p=d,m=a("2877"),f=Object(m["a"])(p,r,n,!1,null,null,null);t["default"]=f.exports},"23c5":function(e,t,a){"use strict";a("ecd0")},"254e":function(e,t,a){"use strict";a("0e2a")},"260c":function(e,t,a){"use strict";a("b157")},"26fc":function(e,t,a){e.exports=a.p+"static/img/404_cloud.0f4bc32b.png"},"2c38":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[a("el-row",[e.isEdit?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"项目凭证",prop:"appKey"}},[a("el-input",{attrs:{disabled:""},model:{value:e.form.appKey,callback:function(t){e.$set(e.form,"appKey",t)},expression:"form.appKey"}})],1)],1):e._e(),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"版本号",prop:"verCode"}},[a("el-input-number",{staticStyle:{width:"35%"},attrs:{placeholder:"请输入版本号"},model:{value:e.form.verCode,callback:function(t){e.$set(e.form,"verCode",t)},expression:"form.verCode"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"内容",prop:"content"}},[a("el-input",{attrs:{type:"textarea",rows:"6",placeholder:"请输入升级内容说明..."},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"升级权重",prop:"upgradeWeight"}},[a("el-input-number",{staticStyle:{width:"35%"},attrs:{placeholder:"请输入升级权重",value:"100"},model:{value:e.form.upgradeWeight,callback:function(t){e.$set(e.form,"upgradeWeight",t)},expression:"form.upgradeWeight"}}),e._v(" "),a("span",{staticClass:"extra"},[e._v("1~100，每次请求升级的概率")])],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"",prop:"enable"}},[a("el-checkbox",{model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}},[e._v("开启升级")])],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"",prop:"force"}},[a("el-checkbox",{model:{value:e.form.force,callback:function(t){e.$set(e.form,"force",t)},expression:"form.force"}},[e._v("强制更新")])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"",prop:"isTest"}},[a("el-checkbox",{model:{value:e.form.isTest,callback:function(t){e.$set(e.form,"isTest",t)},expression:"form.isTest"}},[e._v("启用测试")]),e._v(" "),a("span",{staticClass:"extra"},[e._v("勾选后，测试用户Id才可以收到升级信息")])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"",prop:"isJump"}},[a("el-checkbox",{model:{value:e.form.isJump,callback:function(t){e.$set(e.form,"isJump",t)},expression:"form.isJump"}},[e._v("启用跳转")]),e._v(" "),a("span",{staticClass:"extra",staticStyle:{color:"red"}},[e._v("勾选后，App打开时候将自动跳转")])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"跳转目标包名",prop:"jumpToPackName"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.form.jumpToPackName,callback:function(t){e.$set(e.form,"jumpToPackName",t)},expression:"form.jumpToPackName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"针对版本升级",prop:"targetVer"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.form.targetVer,callback:function(t){e.$set(e.form,"targetVer",t)},expression:"form.targetVer"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"针对用户升级",prop:"targetUids"}},[a("el-input",{attrs:{type:"textarea",rows:"3",placeholder:"请输入用户升级Id..."},model:{value:e.form.targetUids,callback:function(t){e.$set(e.form,"targetUids",t)},expression:"form.targetUids"}}),e._v(" "),a("span",{staticClass:"extra"},[e._v("用户升级Id，可填多个，用,分隔")])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"测试用户ID",prop:"testUids"}},[a("el-input",{attrs:{type:"textarea",rows:"3",placeholder:"请输入测试用户Id..."},model:{value:e.form.testUids,callback:function(t){e.$set(e.form,"testUids",t)},expression:"form.testUids"}}),e._v(" "),a("span",{staticClass:"extra"},[e._v("测试用户Id，可填多个，用,分隔")])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"上传安装包",prop:"packURL"}},[a("x-upload",{attrs:{"upload-data":e.uploadData,"list-type":"text",drag:""},on:{success:e.onUploadSuccess},model:{value:e.form.packURL,callback:function(t){e.$set(e.form,"packURL",t)},expression:"form.packURL"}})],1)],1),e._v(" "),e.form.packURL_Url?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"安装包下载链接"}},[a("a",{attrs:{href:e.form.packURL_Url}},[e._v(e._s(e.form.packURL_Url))])])],1):e._e(),e._v(" "),e.form.packURL_Url?a("el-col",{attrs:{span:24,prop:"packMD5"}},[a("el-form-item",{attrs:{label:"安装包MD5"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.form.packMD5,callback:function(t){e.$set(e.form,"packMD5",t)},expression:"form.packMD5"}})],1)],1):e._e()],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.canChoose?[a("el-radio-group",{attrs:{size:"mini"},on:{change:e.onChooseTypeChnage},model:{value:e.chooseType,callback:function(t){e.chooseType=t},expression:"chooseType"}},[a("el-radio-button",{attrs:{label:"upload"}},[e._v("上传")]),e._v(" "),a("el-radio-button",{attrs:{label:"input"}},[e._v("输入链接")])],1)]:e._e(),e._v(" "),"upload"==e.chooseType?[a("el-upload",e._g(e._b({ref:"upload",attrs:{drag:"",action:e.uploadUrl,headers:e.headers,"list-type":e.listType,"show-file-list":!0,"file-list":e.fileList,data:e.uploadData,limit:e.limit,"on-preview":e.handlePictureCardPreview,"on-success":e.handleUploadSuccess,"on-exceed":e.handleFileExceed,"on-remove":e.handleFileRemove}},"el-upload",e.$attrs,!1),e.$listeners),[a("i",{staticClass:"el-icon-upload"}),e._v(" "),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])])]),e._v(" "),a("el-dialog",{attrs:{visible:e.previewDialogVisible,"append-to-body":!0},on:{"update:visible":function(t){e.previewDialogVisible=t}}},[a("img",{attrs:{width:"100%",src:e.previewDialogImageUrl,alt:""}})])]:[a("el-input",{attrs:{placeholder:"请输入链接"},on:{change:e.onInputUrlChange},model:{value:e.inputUrl,callback:function(t){e.inputUrl=t},expression:"inputUrl"}},[a("div",{attrs:{slot:"append"},slot:"append"},[a("el-button",{attrs:{icon:"el-icon-refresh-right"},on:{click:e.loadInputImage}},[e._v("加载图片")])],1)]),e._v(" "),e.showInputImage?a("el-image",{staticStyle:{width:"200px",height:"200px","margin-top":"10px"},attrs:{src:e.inputUrl},on:{load:e.onInputImageLoad}}):e._e()]],2)},l=[],o=(a("f559"),a("20d6"),a("7f7f"),a("2909")),c=(a("c5f6"),a("5f87")),u=a("365c"),d={inheritAttrs:!1,props:{value:{type:[String,Array]},uploadUrl:{type:String,default:u["i"].uploadUrl},uploadType:{type:String},uploadData:{type:Object},limit:{type:Number,default:1},src:[String,Array],canChoose:{type:Boolean,default:!1},listType:{type:String,default:"picture-card"}},data:function(){return{headers:{},imageUrl:"",fileList:[],previewDialogVisible:!1,previewDialogImageUrl:!1,chooseType:"upload",inputUrl:void 0,showInputImage:!1}},watch:{src:function(e){e?this.canChoose?this.judgeTypeByValue(this.value,e):this.setFileList(e):this.fileList=[]},value:function(e){e||(this.fileList=[]),Array.isArray(e)&&0==e.length&&(this.fileList=[])}},mounted:function(){this.uploadType&&(this.uploadData={uploadType:this.uploadType}),this.headers={"Access-Token":Object(c["c"])()},this.src&&(this.canChoose?this.judgeTypeByValue(this.value,this.src):this.setFileList(this.src))},beforeDestroy:function(){this.fileList=[],this.inputUrl=void 0,this.showInputImage=!1},methods:{handlePictureCardPreview:function(e){this.previewDialogImageUrl=e.url,this.previewDialogVisible=!0},handleUploadSuccess:function(e,t,a){if(0==e.code){var r=e.data,n=r.path,i=r.url;this.imageUrl=i,1==this.limit?(this.$emit("input",n),this.$emit("success",e)):(this.$emit("input",[].concat(Object(o["a"])(this.value),[n])),this.$emit("success",e))}else this.$emit("error",e),this.fileList=[],this.$xMsgError(e.msg)},handleFileExceed:function(e,t){1==this.limit&&(this.$set(t[0],"raw",e[0]),this.$set(t[0],"name",e[0].name),this.$refs.upload.clearFiles(),this.$refs.upload.handleStart(e[0]),this.$refs.upload.submit())},handleFileRemove:function(e,t){if(this.fileList=t,1==this.limit)this.$emit("input",void 0);else{if(void 0!=e.idx)return void this.deleteByIdx(e.idx);var a=e.response.data.path,r=this.value.findIndex((function(e){return e==a}));-1!=r&&this.deleteByIdx(r)}},deleteByIdx:function(e){var t=this.value;t.splice(e,1),this.$emit("input",t)},onChooseTypeChnage:function(){this.showInputImage=!1,this.inputUrl=void 0,this.$emit("input",void 0)},onInputUrlChange:function(){this.$emit("input",this.inputUrl)},loadInputImage:function(){this.inputUrl?this.showInputImage=!0:this.$xMsgError("请输入链接")},onInputImageLoad:function(){},judgeTypeByValue:function(e,t){e&&(this.value.startsWith("http")?(this.chooseType="input",this.inputUrl=t,this.showInputImage=!0):(this.chooseType="upload",this.setFileList(t)))},setFileList:function(e){e?Array.isArray(e)?this.fileList=e.map((function(e,t){return{url:e,idx:t}})):this.fileList=[{url:e}]:this.fileList=[]}}},p=d,m=a("2877"),f=Object(m["a"])(p,s,l,!1,null,null,null),h=f.exports,g=a("60fe"),v=a("ac0d"),b={mixins:[v["a"]],components:{XUpload:h},data:function(){return{title:"",visible:!1,loading:!1,form:{},rules:{appKey:[{required:!0,message:"项目凭证不能为空",trigger:"blur"}],verCode:[{required:!0,message:"版本号不能为空",trigger:"blur"}],title:[{required:!0,message:"标题不能为空",trigger:"blur"}],packPath:[{required:!0,message:"安装包不能为空",trigger:"blur"}]},keyConfig:{},uploadData:{uploadType:g["b"].appUpgradeApk},isEdit:!1}},methods:{add:function(e){this.isEdit=!1,this.keyConfig=e,this.uploadData.appFlag=e.appFlag,this.form.appKey=e.appKey,this.reset(),this.title="添加【".concat(e.appName,"】升级包"),this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,a){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.isEdit=!0,this.keyConfig=a,this.uploadData.appFlag=a.appFlag,this.form.appKey=a.appKey,this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=10,u["d"].getDetail(t.id);case 10:r=e.sent,0==r.code&&(this.form=r.data),this.$xloading.hide();case 13:case"end":return e.stop()}}),e,this)})));function t(t,a){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r,n,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=18;break}if(e.$xloading.show(),r=Object.assign({},e.form),r.appKey=e.keyConfig.appKey,n=r.id,void 0==n){t.next=13;break}return t.next=8,u["d"].editAppUpgradeConfig(r);case 8:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=18;break;case 13:return t.next=15,u["d"].addAppUpgradeConfig(r);case 15:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 18:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},onUploadSuccess:function(e){var t=e.data;this.form.packMD5=t.md5,this.form.packURL_Url=t.url},download:function(e){this.$router.push(e)}}},y=b,w=Object(m["a"])(y,r,n,!1,null,null,null);t["default"]=w.exports},"2de0":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container",attrs:{align:"center"}},[a("h2",[e._v(e._s(e.text))])])},n=[],i=(a("8e6e"),a("ac6a"),a("456d"),a("96cf"),a("1da1")),s=a("ade3"),l=(a("a481"),a("2f62"));function o(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function c(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?o(Object(a),!0).forEach((function(t){Object(s["a"])(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):o(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var u={data:function(){return{text:"登录中...",loading:!1}},mounted:function(){var e=this.$router.history.current.query;if(!e.code)return this.$xMsgError("无效的code，请重试"),void this.$router.replace({path:"/login"});this.getToken(e)},methods:c(c({},Object(l["b"])(["OAuthLogin"])),{},{getToken:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.prev=1,e.next=4,this.OAuthLogin(t).then((function(e){a.$router.replace({path:"/"})}));case 4:this.$xloading.hide(),e.next=12;break;case 7:e.prev=7,e.t0=e["catch"](1),this.$xMsgError("登录失败，"+e.t0),this.$router.replace({path:"/login"}),this.$xloading.hide();case 12:case"end":return e.stop()}}),e,this,[[1,7]])})));function t(t){return e.apply(this,arguments)}return t}()})},d=u,p=a("2877"),m=Object(p["a"])(d,r,n,!1,null,null,null);t["default"]=m.exports},"31fa":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table-column",{attrs:{label:e.labelText,align:"center","min-width":"130"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[e.isDayShow(r,e.day)?[a("div",[e._v(e._s(r[e.propName])+" / "+e._s(r["day"+e.day+"_Percent"])+"%")])]:e._e()]}}])})},n=[],i=(a("c5f6"),a("5a0c")),s=a.n(i),l={props:{day:{type:Number}},computed:{propName:function(){return"day".concat(this.day)},labelText:function(){return this.day+"天后"}},methods:{isDayShow:function(e,t){var a=s()(e.date),r=a.add(t,"day"),n=s()();return!r.isAfter(n,"day")}}},o=l,c=a("2877"),u=Object(c["a"])(o,r,n,!1,null,null,null);t["default"]=u.exports},"32d1":function(e,t,a){},"41e8":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}]},[a("div",{staticClass:"page-title"},[e._v("修改密码")]),e._v(" "),a("el-row",[a("el-col",{attrs:{span:24,offset:2}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.formRules,"label-width":"100px",inline:"",size:"small"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"旧密码",prop:"oldPassword"}},[a("el-input",{attrs:{placeholder:"","show-password":"",autocomplete:"new-password"},model:{value:e.form.oldPassword,callback:function(t){e.$set(e.form,"oldPassword","string"===typeof t?t.trim():t)},expression:"form.oldPassword"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"新密码",prop:"password"}},[a("el-input",{attrs:{placeholder:"","show-password":""},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password","string"===typeof t?t.trim():t)},expression:"form.password"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"确认新密码",prop:"confirmPassword"}},[a("el-input",{attrs:{placeholder:"","show-password":""},model:{value:e.form.confirmPassword,callback:function(t){e.$set(e.form,"confirmPassword","string"===typeof t?t.trim():t)},expression:"form.confirmPassword"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:8,offset:2}},[a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保存")]),e._v(" "),a("el-button",{on:{click:e.resetForm}},[e._v("重置")])],1)],1)],1)],1)],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("7d92"),l=a("365c"),o={data:function(){var e=this,t=function(t,a,r){""===a?r(new Error("请再次输入密码")):a!==e.form.password?r(new Error("两次输入密码不一致!")):r()};return{loading:!1,form:{},formRules:{oldPassword:[{required:!0,message:"不能为空",trigger:"blur"}],password:[{required:!0,message:"不能为空",trigger:"blur"},{min:6,max:16,message:"长度在 6 到 16 个字符"}],confirmPassword:[{required:!0,message:"不能为空",trigger:"blur"},{min:6,max:16,message:"长度在 6 到 16 个字符"},{validator:t}]}}},methods:{submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=20;break}return e.$xloading.show(),r=Object.assign({},e.form),r.oldPassword=Object(s["c"])(r.oldPassword),r.password=Object(s["c"])(r.password),r.confirmPassword=Object(s["c"])(r.confirmPassword),t.next=8,l["q"].changePassword(r);case 8:if(n=t.sent,e.$xloading.hide(),0!=n.code){t.next=19;break}return e.$xMsgSuccess("修改成功"),e.visible=!1,e.resetForm(),t.next=16,e.$store.dispatch("Logout");case 16:setTimeout((function(){e.$router.push("/login")}),1500),t.next=20;break;case 19:e.$xMsgError("操作失败！"+n.msg);case 20:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},resetForm:function(){this.$xResetForm("form")}}},c=o,u=(a("1717"),a("2877")),d=Object(u["a"])(c,r,n,!1,null,"ad9e835a",null);t["default"]=d.exports},4306:function(e,t,a){"use strict";a("62e9")},4395:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-card",[a("el-tabs",{attrs:{"tab-position":"left"}},[a("el-tab-pane",[a("div",{staticClass:"tab-title",attrs:{slot:"label"},slot:"label"},[e._v("修改密码")]),e._v(" "),a("password")],1)],1)],1)],1)],1)],1)},n=[],i=a("41e8"),s={components:{Password:i["default"]}},l=s,o=(a("4306"),a("2877")),c=Object(o["a"])(l,r,n,!1,null,"44d2dc26",null);t["default"]=c.exports},4624:function(e,t,a){"use strict";var r=a("d0a2");a.d(t,"a",(function(){return r["a"]}));a("de11")},"4b3b":function(e,t,a){var r={"./":"1e4b","./404":"8cdb","./404.vue":"8cdb","./auth/yyoauth":"2de0","./auth/yyoauth.vue":"2de0","./channel/channel":"a6f9","./channel/channel-user":"80c7","./channel/channel-user/":"80c7","./channel/channel-user/Edit":"a078","./channel/channel-user/Edit.vue":"a078","./channel/channel-user/index":"80c7","./channel/channel-user/index.vue":"80c7","./channel/channel/":"a6f9","./channel/channel/BatchEdit":"1223","./channel/channel/BatchEdit.vue":"1223","./channel/channel/DownloadDialog":"b5fa","./channel/channel/DownloadDialog.vue":"b5fa","./channel/channel/Edit":"eae9","./channel/channel/Edit.vue":"eae9","./channel/channel/index":"a6f9","./channel/channel/index.vue":"a6f9","./config/appkey-config":"cccda","./config/appkey-config/":"cccda","./config/appkey-config/Edit":"eb73","./config/appkey-config/Edit.vue":"eb73","./config/appkey-config/index":"cccda","./config/appkey-config/index.vue":"cccda","./config/symbol-config":"1ba4","./config/symbol-config/":"1ba4","./config/symbol-config/Edit":"ec0e","./config/symbol-config/Edit.vue":"ec0e","./config/symbol-config/index":"1ba4","./config/symbol-config/index.vue":"1ba4","./dashboard":"9406","./dashboard/":"9406","./dashboard/components/PanelGroup":"3e3b","./dashboard/components/PanelGroup.vue":"3e3b","./dashboard/components/PanelTable":"4f73","./dashboard/components/PanelTable.vue":"4f73","./dashboard/index":"9406","./dashboard/index.vue":"9406","./index":"1e4b","./index.vue":"1e4b","./login":"9ed6","./login/":"9ed6","./login/index":"9ed6","./login/index.vue":"9ed6","./logs/run-log":"9230","./logs/run-log/":"9230","./logs/run-log/index":"9230","./logs/run-log/index.vue":"9230","./logs/symbolrunlog":"d933","./logs/symbolrunlog/":"d933","./logs/symbolrunlog/index":"d933","./logs/symbolrunlog/index.vue":"d933","./stats/analysis":"9301","./stats/analysis/":"9301","./stats/analysis/index":"9301","./stats/analysis/index.vue":"9301","./stats/channel":"af40","./stats/channel-custom":"5a35","./stats/channel-custom-retain":"eeda","./stats/channel-custom-retain/":"eeda","./stats/channel-custom-retain/index":"eeda","./stats/channel-custom-retain/index.vue":"eeda","./stats/channel-custom/":"5a35","./stats/channel-custom/index":"5a35","./stats/channel-custom/index.vue":"5a35","./stats/channel-retain":"59d1","./stats/channel-retain/":"59d1","./stats/channel-retain/DayTableColumn":"31fa","./stats/channel-retain/DayTableColumn.vue":"31fa","./stats/channel-retain/index":"59d1","./stats/channel-retain/index.vue":"59d1","./stats/channel/":"af40","./stats/channel/index":"af40","./stats/channel/index.vue":"af40","./system/account-setting":"4395","./system/account-setting/":"4395","./system/account-setting/index":"4395","./system/account-setting/index.vue":"4395","./system/account-setting/password":"41e8","./system/account-setting/password/":"41e8","./system/account-setting/password/index":"41e8","./system/account-setting/password/index.vue":"41e8","./system/log/login":"f5e8","./system/log/login/":"f5e8","./system/log/login/index":"f5e8","./system/log/login/index.vue":"f5e8","./system/log/oper":"e38b","./system/log/oper/":"e38b","./system/log/oper/DetailDialog":"55cb","./system/log/oper/DetailDialog.vue":"55cb","./system/log/oper/index":"e38b","./system/log/oper/index.vue":"e38b","./system/menu":"f794","./system/menu/":"f794","./system/menu/Edit":"89ee","./system/menu/Edit.vue":"89ee","./system/menu/index":"f794","./system/menu/index.vue":"f794","./system/notice":"202d","./system/notice/":"202d","./system/notice/Edit":"13f9","./system/notice/Edit.vue":"13f9","./system/notice/index":"202d","./system/notice/index.vue":"202d","./system/role":"70eb","./system/role/":"70eb","./system/role/Edit":"c641","./system/role/Edit.vue":"c641","./system/role/index":"70eb","./system/role/index.vue":"70eb","./system/user":"1f34","./system/user/":"1f34","./system/user/Edit":"bb53","./system/user/Edit.vue":"bb53","./system/user/index":"1f34","./system/user/index.vue":"1f34","./tools/gen":"837b","./tools/gen/":"837b","./tools/gen/PreviewDialog":"99b0","./tools/gen/PreviewDialog.vue":"99b0","./tools/gen/index":"837b","./tools/gen/index.vue":"837b","./tools/redis":"f56f","./tools/redis/":"f56f","./tools/redis/index":"f56f","./tools/redis/index.vue":"f56f","./upgrade/upgrade-config":"4fe9","./upgrade/upgrade-config/":"4fe9","./upgrade/upgrade-config/edit":"2c38","./upgrade/upgrade-config/edit.vue":"2c38","./upgrade/upgrade-config/index":"4fe9","./upgrade/upgrade-config/index.vue":"4fe9"};function n(e){var t=i(e);return a(t)}function i(e){var t=r[e];if(!(t+1)){var a=new Error("Cannot find module '"+e+"'");throw a.code="MODULE_NOT_FOUND",a}return t}n.keys=function(){return Object.keys(r)},n.resolve=i,e.exports=n,n.id="4b3b"},"4fe9":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["appUpgradeConfig:add"],expression:"['appUpgradeConfig:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"title",label:"标题",align:"center","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"verCode",label:"版本号",align:"center","min-width":"80"}}),e._v(" "),a("el-table-column",{attrs:{label:"升级权重",align:"center","min-width":"90"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",[e._v(e._s(r.upgradeWeight)+"%")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"URL",align:"center","min-width":"350"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("a",{attrs:{href:r.packURL_Url}},[e._v(e._s(r.packURL))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"packMD5",label:"MD5",align:"center","min-width":"300"}}),e._v(" "),a("el-table-column",{attrs:{label:"强制更新",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.force?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"启用更新",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.enable?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"启用测试",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.isTest?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"跳转App",align:"center","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.isJump?a("el-tag",{attrs:{type:"success"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger"}},[e._v("否")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"createOn",label:"添加时间",align:"center","min-width":"180"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["appUpgradeConfig:edit"],expression:"['appUpgradeConfig:edit']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-edit"},on:{click:function(t){return e.handleEdit(r)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["appUpgradeConfig:delete"],expression:"['appUpgradeConfig:delete']"}],attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.handleDelete(r)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("4624"),l=a("365c"),o=a("2c38"),c={components:{EditDialog:o["default"]},mixins:[s["a"]],data:function(){return{queryParams:{},tableData:{},loading:!1,keyConfig:{}}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,a,r=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=this.$router.history.current.query,t.appKey){e.next=4;break}return this.$xMsgError("缺少参数[appKey]"),e.abrupt("return");case 4:return this.queryParams.appKey=t.appKey,e.next=7,this.loadKeyConfig(t.appKey);case 7:a=e.sent,this.$nextTick((function(){r.keyConfig=a}));case 9:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["d"].getList(t);case 4:a=e.sent,this.tableData=a,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add(this.keyConfig)},handleEdit:function(e){this.$refs.editDialog.edit(e,this.keyConfig)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,l["d"].delAppUpgradeConfig(t.id);case 3:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("删除成功")):a.$xMsgError("删除失败！"+r.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},loadKeyConfig:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l["b"].getDetailByKey(t);case 2:return a=e.sent,e.abrupt("return",a.data);case 4:case"end":return e.stop()}}),e)})));function t(t){return e.apply(this,arguments)}return t}()}},u=c,d=a("2877"),p=Object(d["a"])(u,r,n,!1,null,null,null);t["default"]=p.exports},"55cb":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"900px"},on:{"update:visible":function(t){e.visible=t},close:e.onClose}},[a("el-divider",{attrs:{"content-position":"left"}},[e._v(e._s(e.subTitle))]),e._v(" "),a("el-row",{attrs:{gutter:20}},[e.oldValue?a("el-col",{attrs:{span:12}},[a("div",{staticClass:"waring"},[a("json-viewer",{attrs:{value:e.oldValue,boxed:"",copyable:""}})],1)]):e._e(),e._v(" "),e.newValue?a("el-col",{attrs:{span:12}},[a("div",{staticClass:"success"},[a("json-viewer",{attrs:{value:e.newValue,boxed:"",copyable:""}})],1)]):e._e(),e._v(" "),e.desc?a("el-col",{attrs:{span:24}},[a("div",{staticClass:"waring"},[a("json-viewer",{attrs:{value:e.desc,boxed:"",copyable:""}})],1)]):e._e()],1),e._v(" "),e.isFailure?[a("el-divider",{attrs:{"content-position":"left"}},[e._v("异常信息")]),e._v(" "),e.desc?a("el-col",{attrs:{span:24}},[a("div",{staticClass:"waring"},[a("pre",[e._v("          异常信息："+e._s(e.exception.msg)+"\n          异常类型："+e._s(e.exception.type)+"\n          异常堆栈："+e._s(e.exception.stackTrace)+"\n        ")])])]):e._e()]:e._e(),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.visible=!1}}},[e._v("取 消")])],1)],2)},n=[],i=a("60fe"),s=a("349e"),l=a.n(s),o={components:{JsonViewer:l.a},data:function(){return{title:"详情",visible:!1,loading:!1,subTitle:"",oldValue:"",newValue:"",isFailure:!1,desc:"",exception:{}}},methods:{show:function(e){if(this.visible=!0,"0"!=e.status)return this.isFailure=!0,this.subTitle="请求数据",e.desc&&(this.desc=JSON.parse(e.desc)),this.exception.msg=e.exceptionMsg,this.exception.type=e.exceptionType,void(this.exception.stackTrace=e.exceptionStackTrace);e.operateType==i["OperateType"].Add?(this.subTitle="新增的数据",this.newValue=JSON.parse(e.newValue)):e.operateType==i["OperateType"].Edit?(this.subTitle="修改的数据",e.oldValue&&(this.oldValue=JSON.parse(e.oldValue)),this.newValue=JSON.parse(e.newValue)):(this.subTitle="详情",this.subTitle="无")},onClose:function(){this.oldValue=void 0,this.newValue=void 0,this.desc=void 0,this.isFailure=!1}}},c=o,u=(a("8490"),a("8f91"),a("2877")),d=Object(u["a"])(c,r,n,!1,null,"8823c306",null);t["default"]=d.exports},"59d1":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{"label-width":"80px",inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"所属项目"}},[a("x-select",{attrs:{"show-default":"",customRender:"",url:"/appKeyConfig/options"},on:{change:e.handleAppIdChange},model:{value:e.queryParams.appId,callback:function(t){e.$set(e.queryParams,"appId",t)},expression:"queryParams.appId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"渠道"}},[a("x-select",{attrs:{"show-default":"",goroup:"",options:e.channelOptions,url:"/channel/options"},model:{value:e.queryParams.channelId,callback:function(t){e.$set(e.queryParams,"channelId",t)},expression:"queryParams.channelId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight,"cell-style":e.handleCellStyle}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"date",label:"日期",align:"center",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"day0",label:"排重安装数",align:"center","min-width":"100"}}),e._v(" "),a("day-table-column",{attrs:{day:1}}),e._v(" "),a("day-table-column",{attrs:{day:2}}),e._v(" "),a("day-table-column",{attrs:{day:3}}),e._v(" "),a("day-table-column",{attrs:{day:4}}),e._v(" "),a("day-table-column",{attrs:{day:5}}),e._v(" "),a("day-table-column",{attrs:{day:6}}),e._v(" "),a("day-table-column",{attrs:{day:7}})],1)],1)],1)},n=[],i=(a("4917"),a("96cf"),a("1da1")),s=a("5a0c"),l=a.n(s),o=a("4624"),c=a("365c"),u=a("31fa"),d={components:{DayTableColumn:u["default"]},mixins:[o["a"]],data:function(){return{queryParams:{appId:-1,channelId:-1},loading:!1,tableData:{},channelOptions:[]}},created:function(){this.reset()},methods:{queryReset:function(){this.queryParams={appId:-1,channelId:-1},this.reset(),this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date[0],t.endTime=this.queryParams.date[1]),e.next=5,c["n"].getChannelRetainList(t);case 5:a=e.sent,this.tableData=a,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){var e=l()().subtract(10,"day").format("YYYY-MM-DD"),t=l()().format("YYYY-MM-DD");this.queryParams.date=[e,t]},handleCellStyle:function(e){var t=e.row,a=e.column,r=(e.rowIndex,e.columnIndex,a.label),n=/^[1-9]+/g,i=r.match(n);if(i){var s=i[0],l=this.isDayShow(t,s);if(l){var o=null,c=t["day".concat(s,"_Percent")];if(0!=c)return o=c<10?"#e6fcf5":c<20?"#c3fae8":c<30?"#96f2d7":c<50?"#63e6be":c<60?"#38d9a9":"#20c997",o?"background: ".concat(o,";"):void 0}}},isDayShow:function(e,t){var a=l()(e.date),r=a.add(t,"day"),n=l()();return!r.isAfter(n,"day")},handleAppIdChange:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.queryParams.channelId=-1,e.next=3,c["f"].getOptions({appId:this.queryParams.appId});case 3:t=e.sent,0==t.code&&(this.channelOptions=t.data);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},p=d,m=a("2877"),f=Object(m["a"])(p,r,n,!1,null,null,null);t["default"]=f.exports},"5a35":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{"label-width":"80px",inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"所属项目"}},[a("x-select",{attrs:{"show-default":"",customRender:"",url:"/appKeyConfig/options"},on:{change:e.handleAppIdChange},model:{value:e.queryParams.appId,callback:function(t){e.$set(e.queryParams,"appId",t)},expression:"queryParams.appId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"渠道"}},[a("x-select",{attrs:{"show-default":"",goroup:"",options:e.channelOptions,url:"/channel/options"},model:{value:e.queryParams.channelId,callback:function(t){e.$set(e.queryParams,"channelId",t)},expression:"queryParams.channelId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"锚点"}},[a("x-select",{attrs:{options:e.symbolOptions,url:"/symbolConfig/options"},model:{value:e.queryParams.symbol,callback:function(t){e.$set(e.queryParams,"symbol",t)},expression:"queryParams.symbol"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"date",label:"日期",align:"center",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"symbol",label:"锚点标识",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"symbolName",label:"锚点名称",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"value",label:"数值",align:"center","min-width":"100"},scopedSlots:e._u([{key:"header",fn:function(){return[a("x-table-header-tip",{attrs:{label:"数值",tip:"按锚点类型不同数值标识的数据也不一样"}})]},proxy:!0}])}),e._v(" "),a("el-table-column",{attrs:{prop:"count",label:"锚点总数量",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"valueNew",label:"新用户数值",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"countNew",label:"新用户锚点数量",align:"center","min-width":"100"}})],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("5a0c"),l=a.n(s),o=a("4624"),c=a("365c"),u={mixins:[o["a"]],data:function(){return{queryParams:{appId:-1,channelId:-1,symbol:""},loading:!1,tableData:{},channelOptions:[],symbolOptions:[]}},created:function(){this.reset()},methods:{queryReset:function(){this.queryParams={appId:-1,channelId:-1,symbol:""},this.reset(),this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date[0],t.endTime=this.queryParams.date[1]),e.next=5,c["n"].getChannelCustomStatsList(t);case 5:a=e.sent,this.tableData=a,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){var e=l()().format("YYYY-MM-DD"),t=l()().format("YYYY-MM-DD");this.queryParams.date=[e,t]},handleAppIdChange:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.queryParams.channelId=-1,e.next=3,c["f"].getOptions({appId:this.queryParams.appId});case 3:t=e.sent,0==t.code&&(this.channelOptions=t.data);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},d=u,p=a("2877"),m=Object(p["a"])(d,r,n,!1,null,null,null);t["default"]=m.exports},"60fe":function(e,t,a){"use strict";a.d(t,"c",(function(){return r})),a.d(t,"b",(function(){return n}));var r=[{label:"正常",value:1},{label:"异常",value:2},{label:"作弊",value:3},{label:"测试用户",value:99}],n={appTabIcon:"appTabIcon",appBanner:"appBanner",appButton:"appButton",appPoster:"appPoster",appSystemNotice:"appSystemNotice",appUpgradeApk:"appUpgradeApk"}},"62e9":function(e,t,a){},6776:function(e,t,a){},"70eb":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"id",label:"ID",align:"center",width:"80"}}),e._v(" "),a("el-table-column",{attrs:{prop:"roleName",label:"角色名称","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"sort",label:"排序",align:"center",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"status",label:"状态",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{disabled:!e.changeStatusPermission,"active-value":!0,"inactive-value":!1},on:{change:function(a){return e.onStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"180",align:"center","class-name":"small-padding"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:role:edit"],expression:"['system:role:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(a){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:role:delete"],expression:"['system:role:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("4624"),l=a("3528"),o=a("c641"),c={mixins:[s["a"]],components:{EditDialog:o["default"]},data:function(){return{queryParams:{},loading:!1,tableData:{},changeStatusPermission:!1}},mounted:function(){this.changeStatusPermission=this.$hasPermission("system:role:changeStatus")},methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["f"](t);case 4:a=e.sent,this.tableData=a,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,l["c"](t.id);case 3:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("删除成功")):a.$xMsgError("删除失败！"+r.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},onStatusChange:function(e){var t=this,a=0==e.status?"启用":"禁用";this.$confirm("是否确认【".concat(a,"】该角色？"),"提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function a(){var r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,l["b"](e.id,e.status);case 2:r=a.sent,0==r.code?t.$xMsgSuccess("操作成功"):t.$xMsgError("操作失败！"+r.msg),t.$refs.table.refresh();case 5:case"end":return a.stop()}}),a)})))).catch((function(){t.$refs.table.refresh()}))}}},u=c,d=a("2877"),p=Object(d["a"])(u,r,n,!1,null,null,null);t["default"]=p.exports},"7d92":function(e,t,a){"use strict";a.d(t,"c",(function(){return u})),a.d(t,"b",(function(){return m})),a.d(t,"a",(function(){return f}));a("f576"),a("ed50"),a("6b54");var r=a("3452"),n=a.n(r),i=(a("c198"),a("720d")),s=a.n(i);n.a.mode.CBC,n.a.pad.Pkcs7;var l="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMfYk/CK1aLSxfIsQ94dXXsgbXVRLlva\nCdQl3egrNJ1+s0WJqgyPdFDIFw/TiwW6cVtYKN5Tm12rC1aVUhE7Mc0CAwEAAQ==",o="MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBALkumIqQMhoRbM5opek++pTvyB/VZCtH\nJJqXj0olueiarjwMqFiVgyQjwREddT1y5Sijj1PH9GPLs4GLv9y/y50CAwEAAQ==",c="MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAuS6YipAyGhFszmil\n6T76lO/IH9VkK0ckmpePSiW56JquPAyoWJWDJCPBER11PXLlKKOPU8f0Y8uzgYu/\n3L/LnQIDAQABAkBZhV8UzTSLSZUyC4D5SwrUaT5ztTMhgNj/KvmIPMis2xc+1KRS\nKlSSx00+qdm4bKDSB0D80MI5r2FDyov7YUe5AiEA5gT9d+U4bBSguBara4sg62UG\nhp21XPsAdWVhpHg0+rsCIQDOGSF9asLMhNreIwgy88zVp/IkyBf/MGmsZYj82Y0J\nhwIgBOnmYEFNS0HFjSku0EVQlra5xPZpgWr7P4bC5ziKKTECIQDL900QjQbqVxUw\nQGVN38A5NrPKuQgesm/ygK345ujQowIgPNvJ7AO+SS+MPHdnVn9pk7Jwcmlo0aQb\nz85W+KC0dBE=";function u(e){return d(e,l)}function d(e,t){var a=new s.a;return a.setPublicKey(t),a.encrypt(e)}function p(e,t){var a=new s.a;return a.setPrivateKey(t),a.decrypt(e)}function m(e){return d(e,o)}function f(e){return p(e,c)}},"7fe1":function(e,t,a){},"80c7":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["channelUser:add"],expression:"['channelUser:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"id",label:"id",align:"center",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"userName",label:"渠道账号",align:"center","min-width":"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(a){return e.handleCustomerLogin(t.row.userName)}}},[a("div",[e._v(e._s(t.row.userName))])])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"nickName",label:"渠道名称",align:"center","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"lossRate",label:"扣量率",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"enable",label:"状态",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.enable?a("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("否")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"addTime",label:"添加时间",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"editTime",label:"修改时间",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"260",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["channelUser:resetPassword"],expression:"['channelUser:resetPassword']"}],attrs:{size:"mini"},on:{click:function(a){return e.handleResetPassword(t.row)}}},[e._v("重置密码")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["channelUser:edit"],expression:"['channelUser:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(a){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["channelUser:delete"],expression:"['channelUser:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("f645"),l=a("4624"),o=a("365c"),c=a("a078"),u={components:{EditDialog:c["default"]},mixins:[l["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["g"].getList(t);case 4:a=e.sent,this.tableData=a,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,o["g"].delchannelUser(t.id);case 3:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("删除成功")):a.$xMsgError("删除失败！"+r.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleResetPassword:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$xloading.show(),this.$confirm("是否确认重置该用户的密码","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["g"].resetPassword(t.id);case 2:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("重置密码成功")):a.$xMsgError("操作失败！"+r.msg);case 5:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 2:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleCustomerLogin:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,o["g"].customerLogin(t);case 2:a=e.sent,0==a.code?(r=s["a"].customerAdminLoginUrl+"?username=".concat(t,"&token=").concat(a.data),window.open(r)):this.$xMsgError("操作失败！"+a.msg);case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},d=u,p=a("2877"),m=Object(p["a"])(d,r,n,!1,null,null,null);t["default"]=m.exports},8206:function(e){e.exports=["platform-eleme","eleme","delete-solid","delete","s-tools","setting","user-solid","user","phone","phone-outline","more","more-outline","star-on","star-off","s-goods","goods","warning","warning-outline","question","info","remove","circle-plus","success","error","zoom-in","zoom-out","remove-outline","circle-plus-outline","circle-check","circle-close","s-help","help","minus","plus","check","close","picture","picture-outline","picture-outline-round","upload","upload2","download","camera-solid","camera","video-camera-solid","video-camera","message-solid","bell","s-cooperation","s-order","s-platform","s-fold","s-unfold","s-operation","s-promotion","s-home","s-release","s-ticket","s-management","s-open","s-shop","s-marketing","s-flag","s-comment","s-finance","s-claim","s-custom","s-opportunity","s-data","s-check","s-grid","menu","share","d-caret","caret-left","caret-right","caret-bottom","caret-top","bottom-left","bottom-right","back","right","bottom","top","top-left","top-right","arrow-left","arrow-right","arrow-down","arrow-up","d-arrow-left","d-arrow-right","video-pause","video-play","refresh","refresh-right","refresh-left","finished","sort","sort-up","sort-down","rank","loading","view","c-scale-to-original","date","edit","edit-outline","folder","folder-opened","folder-add","folder-remove","folder-delete","folder-checked","tickets","document-remove","document-delete","document-copy","document-checked","document","document-add","printer","paperclip","takeaway-box","search","monitor","attract","mobile","scissors","umbrella","headset","brush","mouse","coordinate","magic-stick","reading","data-line","data-board","pie-chart","data-analysis","collection-tag","film","suitcase","suitcase-1","receiving","collection","files","notebook-1","notebook-2","toilet-paper","office-building","school","table-lamp","house","no-smoking","smoking","shopping-cart-full","shopping-cart-1","shopping-cart-2","shopping-bag-1","shopping-bag-2","sold-out","sell","present","box","bank-card","money","coin","wallet","discount","price-tag","news","guide","male","female","thumb","cpu","link","connection","open","turn-off","set-up","chat-round","chat-line-round","chat-square","chat-dot-round","chat-dot-square","chat-line-square","message","postcard","position","turn-off-microphone","microphone","close-notification","bangzhu","time","odometer","crop","aim","switch-button","full-screen","copy-document","mic","stopwatch","medal-1","medal","trophy","trophy-1","first-aid-kit","discover","place","location","location-outline","location-information","add-location","delete-location","map-location","alarm-clock","timer","watch-1","watch","lock","unlock","key","service","mobile-phone","bicycle","truck","ship","basketball","football","soccer","baseball","wind-power","light-rain","lightning","heavy-rain","sunrise","sunrise-1","sunset","sunny","cloudy","partly-cloudy","cloudy-and-sunny","moon","moon-night","dish","dish-1","food","chicken","fork-spoon","knife-fork","burger","tableware","sugar","dessert","ice-cream","hot-water","water-cup","coffee-cup","cold-drink","goblet","goblet-full","goblet-square","goblet-square-full","refrigerator","grape","watermelon","cherry","apple","pear","orange","coffee","ice-tea","ice-drink","milk-tea","potato-strips","lollipop","ice-cream-square","ice-cream-round"]},"837b":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-alert",{attrs:{title:"说明",type:"warning",closable:!1}},[a("div",[e._v("\n      用于生成数据库表对应的实体类、后台CRUD代码\n    ")])]),e._v(" "),a("el-card",{staticClass:"mt-20"},[a("el-tabs",{on:{"tab-click":e.handleTabClick},model:{value:e.tabName,callback:function(t){e.tabName=t},expression:"tabName"}},e._l(e.dbList,(function(e){return a("el-tab-pane",{key:e.value,attrs:{label:e.label}})})),1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{autoLoad:!1,data:e.tableData,"row-key":"id",loadData:e.getList}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"tableName",label:"表名"}}),e._v(" "),a("el-table-column",{attrs:{prop:"tableComment",label:"表描述"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handlePreview(t.row)}}},[e._v("预览")]),e._v(" "),a("el-button",{attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleDownload(t.row)}}},[e._v("下载")])]}}])})],1)],1),e._v(" "),a("preview-dialog",{ref:"preview"})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("99b0"),l=a("365c"),o=(a("4624"),a("a481"),a("3b2b"),a("bc3a")),c=a.n(o),u=a("5f87"),d=a("5c96"),p={xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",zip:"application/zip"},m="/api";function f(e,t,a){var r,n=m+t;e&&(r=d["Loading"].service()),c()({method:"get",url:n,responseType:"blob",headers:{"Access-Token":Object(u["c"])()}}).then((function(t){e&&r.close(),h(t,p.zip)}))}function h(e,t){var a=document.createElement("a"),r=new Blob([e.data],{type:t}),n=new RegExp("filename=([^;]+\\.[^\\.;]+);*"),i=decodeURI(e.headers["content-disposition"]),s=n.exec(i),l=s[1];l=l.replace(/\"/g,""),a.href=URL.createObjectURL(r),a.setAttribute("download",l),document.body.appendChild(a),a.click(),document.body.appendChild(a)}var g={components:{PreviewDialog:s["default"]},mixins:[],data:function(){return{loading:!1,tableData:{},queryParams:{},tabName:"",dbList:[],canDownload:!0}},mounted:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.loadData();case 2:this.reloadByDbIndex(0);case 3:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["genApi"].getTableList(t);case 4:a=e.sent,this.tableData=a,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),loadData:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l["l"].getDatabase();case 2:t=e.sent,this.dbList=t.data;case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),reloadByDbIndex:function(e){this.queryParams.database=this.dbList[e].value,this.$refs.table.refresh(!0)},handleTabClick:function(e){this.reloadByDbIndex(e.index)},handlePreview:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a={tableName:t.tableName,database:this.queryParams.database},this.$refs.preview.open(a);case 2:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleDownload:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a,r=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.canDownload){e.next=3;break}return this.$xMsgError("操作太快了，稍候再试~"),e.abrupt("return");case 3:({tableName:t.tableName,database:this.queryParams.database}),a="/system/gen/download?tableName=".concat(t.tableName,"&database=").concat(this.queryParams.database),f(!0,a),this.canDownload=!1,setTimeout((function(){r.canDownload=!0}),6e3);case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},v=g,b=a("2877"),y=Object(b["a"])(v,r,n,!1,null,null,null);t["default"]=y.exports},8490:function(e,t,a){"use strict";a("1ccc")},8895:function(e,t,a){},"89ee":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"上级菜单",prop:"parentId"}},[a("treeselect",{attrs:{options:e.parentMenus,placeholder:"选择上级菜单"},model:{value:e.form.parentId,callback:function(t){e.$set(e.form,"parentId",t)},expression:"form.parentId"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"菜单类型",prop:"menuType"}},[a("el-radio-group",{model:{value:e.form.menuType,callback:function(t){e.$set(e.form,"menuType",t)},expression:"form.menuType"}},[a("el-radio",{attrs:{label:0}},[e._v("目录")]),e._v(" "),a("el-radio",{attrs:{label:1}},[e._v("菜单")]),e._v(" "),a("el-radio",{attrs:{label:2}},[e._v("按钮")])],1)],1)],1),e._v(" "),a("el-col",{attrs:{xs:e.formItemCol.xs,md:e.formItemCol.md}},[a("el-form-item",{attrs:{label:"菜单名称",prop:"menuName"}},[a("el-input",{attrs:{placeholder:"请输入菜单名称"},model:{value:e.form.menuName,callback:function(t){e.$set(e.form,"menuName",t)},expression:"form.menuName"}})],1)],1),e._v(" "),a("el-col",{attrs:{xs:e.formItemCol.xs,md:e.formItemCol.md}},[a("el-form-item",{attrs:{label:"显示排序",prop:"sort"}},[a("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1)],1),e._v(" "),"2"!=e.form.menuType?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"菜单图标",prop:"icon"}},[a("el-input",{attrs:{placeholder:"请输入图标类名"},model:{value:e.form.icon,callback:function(t){e.$set(e.form,"icon",t)},expression:"form.icon"}},[a("template",{slot:"prepend"},[a("x-icon",{attrs:{icon:e.form.icon}})],1),e._v(" "),a("el-button",{attrs:{slot:"append",icon:"el-icon-thumb"},on:{click:e.openIconsDialog},slot:"append"})],2)],1)],1):e._e(),e._v(" "),"2"!=e.form.menuType?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"路由地址",prop:"path"}},[a("el-input",{attrs:{placeholder:"请输入路由地址"},model:{value:e.form.path,callback:function(t){e.$set(e.form,"path",t)},expression:"form.path"}})],1)],1):e._e(),e._v(" "),"1"==e.form.menuType?a("el-col",{attrs:{xs:e.formItemCol.xs,md:e.formItemCol.md}},[a("el-form-item",{attrs:{label:"组件路径",prop:"component"}},[a("el-input",{attrs:{placeholder:"请输入组件路径"},model:{value:e.form.component,callback:function(t){e.$set(e.form,"component",t)},expression:"form.component"}})],1)],1):e._e(),e._v(" "),a("el-col",{attrs:{xs:e.formItemCol.xs,md:e.formItemCol.md}},["0"!=e.form.menuType?a("el-form-item",{attrs:{label:"权限标识",prop:"permission"}},[a("el-input",{attrs:{placeholder:"请权限标识",maxlength:"50"},model:{value:e.form.permission,callback:function(t){e.$set(e.form,"permission",t)},expression:"form.permission"}})],1):e._e()],1),e._v(" "),"2"!=e.form.menuType?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"菜单状态",prop:"isShow"}},[a("el-radio-group",{model:{value:e.form.isShow,callback:function(t){e.$set(e.form,"isShow",t)},expression:"form.isShow"}},[a("el-radio",{attrs:{label:!0}},[e._v("显示")]),e._v(" "),a("el-radio",{attrs:{label:!1}},[e._v("隐藏")])],1)],1)],1):e._e()],1)],1),e._v(" "),a("icons-dialog",{attrs:{visible:e.iconsDialogVisible,current:e.form.icon},on:{"update:visible":function(t){e.iconsDialogVisible=t},select:e.onIconSelect}})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("ac0d"),l=a("a6dc"),o=a("ca17"),c=a.n(o),u=(a("542c"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",e._g(e._b({attrs:{"append-to-body":"",width:e.dialogWidth,"custom-class":"icon-dialog"},on:{open:e.onOpen,close:e.onClose}},"el-dialog",e.$attrs,!1),e.$listeners),[a("div",{attrs:{slot:"title"},slot:"title"},[e._v("\n      选择图标\n      "),a("el-input",{style:{width:"260px"},attrs:{size:"mini",placeholder:"请输入图标名称","prefix-icon":"el-icon-search",clearable:""},model:{value:e.key,callback:function(t){e.key=t},expression:"key"}})],1),e._v(" "),a("el-tabs",[a("el-tab-pane",{attrs:{label:"element-ui"}},[a("ul",{staticClass:"icon-ul"},e._l(e.iconList,(function(t){return a("li",{key:t,class:e.active===t?"active-item":"",on:{click:function(a){return e.onSelect(t)}}},[a("i",{class:t}),e._v(" "),a("div",[e._v(e._s(t))])])})),0)]),e._v(" "),a("el-tab-pane",{attrs:{label:"自定义"}},[a("ul",{staticClass:"icon-ul"},e._l(e.svgIconList,(function(t){return a("li",{key:t,class:e.active===t?"active-item":"",on:{click:function(a){return e.onSelect(t)}}},[a("svg-icon",{attrs:{"icon-class":t}}),e._v(" "),a("div",[e._v(e._s(t))])],1)})),0)])],1)],1)],1)}),d=[],p=(a("ac6a"),a("8206")),m=p.map((function(e){return"el-icon-".concat(e)})),f=[],h=a("51ff");h.keys().forEach((function(e){var t=e.substring(2,e.length-4);f.push(t)}));var g={inheritAttrs:!1,props:["current"],data:function(){return{dialogWidth:"980px",iconList:m,svgIconList:f,active:null,key:""}},watch:{key:function(e){this.iconList=e?m.filter((function(t){return t.indexOf(e)>-1})):m}},methods:{onOpen:function(){this.active=this.current,this.key=""},onClose:function(){},onSelect:function(e){this.active=e,this.$emit("select",e),this.$emit("update:visible",!1)}}},v=g,b=(a("db6a"),a("1cfa"),a("2877")),y=Object(b["a"])(v,u,d,!1,null,"7abf13ca",null),w=y.exports,x={components:{Treeselect:c.a,IconsDialog:w},mixins:[s["a"]],data:function(){return{formItemCol:{xs:24,md:12},title:"",visible:!1,loading:!1,form:{},rules:{menuType:[{required:!0,message:"不能为空",trigger:"blur"}],menuName:[{required:!0,message:"不能为空",trigger:"blur"}],sort:[{required:!0,message:"不能为空",trigger:"blur"}],path:[{required:!0,message:"不能为空",trigger:"blur"}],isShow:[{required:!0,message:"不能为空",trigger:"blur"}],systemCode:[{required:!0,message:"不能为空",trigger:"blur"}]},parentMenus:[],iconsDialogVisible:!1}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));function t(){return e.apply(this,arguments)}return t}(),mounted:function(){},methods:{add:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="新增",this.visible=!0,e.next=5,this.getTreeData();case 5:t&&this.$set(this.form,"parentId",t.id);case 6:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,this.getTreeData();case 6:return e.next=8,l["getMenu"](t.id);case 8:a=e.sent,0==a.code&&(this.form=a.data,this.$xloading.hide());case 10:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){this.form={isShow:!0},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=15;break}if(e.$xloading.show(),void 0==e.form.id){t.next=10;break}return t.next=5,l["editMenu"](e.form);case 5:r=t.sent,e.$xloading.hide(),0==r.code?(e.$xMsgSuccess("修改成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+r.msg),t.next=15;break;case 10:return t.next=12,l["addMenu"](e.form);case 12:n=t.sent,e.$xloading.hide(),0==n.code?(e.$xMsgSuccess("添加成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+n.msg);case 15:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},getTreeData:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l["getTreeData"](!0);case 2:t=e.sent,this.parentMenus=t.data;case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),openIconsDialog:function(){this.iconsDialogVisible=!0},onIconSelect:function(e){this.form.icon=e}}},_=x,k=Object(b["a"])(_,r,n,!1,null,null,null);t["default"]=k.exports},"8cdb":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wscn-http404-container"},[a("div",{staticClass:"wscn-http404"},[e._m(0),e._v(" "),a("div",{staticClass:"bullshit"},[a("div",{staticClass:"bullshit__oops"},[e._v("呀!!")]),e._v(" "),a("div",{staticClass:"bullshit__info"}),e._v(" "),a("div",{staticClass:"bullshit__headline"},[e._v(e._s(e.message))]),e._v(" "),a("div",{staticClass:"bullshit__info"},[e._v("\n        页面不存在，请检查链接是否正确，或者点击下面的按钮返回首页\n      ")]),e._v(" "),a("a",{staticClass:"bullshit__return-home",attrs:{href:"/"}},[e._v("返回首页")])])])])},n=[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"pic-404"},[r("img",{staticClass:"pic-404__parent",attrs:{src:a("a36b"),alt:"404"}}),e._v(" "),r("img",{staticClass:"pic-404__child left",attrs:{src:a("26fc"),alt:"404"}}),e._v(" "),r("img",{staticClass:"pic-404__child mid",attrs:{src:a("26fc"),alt:"404"}}),e._v(" "),r("img",{staticClass:"pic-404__child right",attrs:{src:a("26fc"),alt:"404"}})])}],i={name:"Page404",computed:{message:function(){return"这里什么都没有..."}}},s=i,l=(a("c89d"),a("2877")),o=Object(l["a"])(s,r,n,!1,null,"e6261d8c",null);t["default"]=o.exports},"8f91":function(e,t,a){"use strict";a("e702")},9230:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"date","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"所属项目"}},[a("x-select",{attrs:{"show-default":"",customRender:"",url:"/appKeyConfig/flagOptions"},model:{value:e.queryParams.appFlag,callback:function(t){e.$set(e.queryParams,"appFlag",t)},expression:"queryParams.appFlag"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"启动类型"}},[a("x-select",{attrs:{"show-default":"",options:e.runTypeOptions},model:{value:e.queryParams.runType,callback:function(t){e.$set(e.queryParams,"runType",t)},expression:"queryParams.runType"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"安卓Id"}},[a("el-input",{model:{value:e.queryParams.deviceId,callback:function(t){e.$set(e.queryParams,"deviceId",t)},expression:"queryParams.deviceId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"渠道Id"}},[a("el-input",{model:{value:e.queryParams.channelId,callback:function(t){e.$set(e.queryParams,"channelId",t)},expression:"queryParams.channelId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"包渠道Id"}},[a("el-input",{model:{value:e.queryParams.packCId,callback:function(t){e.$set(e.queryParams,"packCId",t)},expression:"queryParams.packCId"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"runType",label:"启动类型",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[0==r.runType?a("el-tag",{attrs:{size:"mini"}},[e._v("安装")]):1==r.runType?a("el-tag",{attrs:{size:"mini"}},[e._v("启动")]):2==r.runType?a("el-tag",{attrs:{size:"mini"}},[e._v("退出")]):3==r.runType?a("el-tag",{attrs:{size:"mini"}},[e._v("卸载")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"channelId",label:"渠道Id",align:"center",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"packCId",label:"包渠道Id",align:"center",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"channelName",label:"渠道名称",align:"center",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appFlag",label:"App标识",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"pkgName",label:"包名",align:"center","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"ver",label:"Ver",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"verCode",label:"版本号",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"deviceId",label:"安卓Id",align:"center","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"ip",label:"Ip",align:"center","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"brand",label:"品牌",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"model",label:"型号",align:"center","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"screen",label:"屏幕",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createOn",label:"时间",align:"center",width:"180"}})],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("5a0c"),l=a.n(s),o=a("4624"),c=a("365c"),u={mixins:[o["a"]],data:function(){return{queryParams:{appFlag:-1,runType:-1,date:""},loading:!1,tableData:{},runTypeOptions:[{label:"安装",value:0},{label:"启动",value:1},{label:"退出",value:2}]}},created:function(){this.reset()},methods:{queryReset:function(){this.queryParams={appFlag:-1,runType:-1,date:l()().format("YYYY-MM-DD")},this.$refs.table.refresh(!0)},reset:function(){var e=l()().format("YYYY-MM-DD");this.queryParams.date=e},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date),e.next=5,c["c"].getList(t);case 5:a=e.sent,this.tableData=a,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,c["c"].delAppRunLog(t.id);case 3:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("删除成功")):a.$xMsgError("删除失败！"+r.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()}}},d=u,p=a("2877"),m=Object(p["a"])(d,r,n,!1,null,null,null);t["default"]=m.exports},9301:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"所属项目"}},[a("x-select",{attrs:{"show-default":"",customRender:"",url:"/appKeyConfig/options"},model:{value:e.queryParams.appFlag,callback:function(t){e.$set(e.queryParams,"appFlag",t)},expression:"queryParams.appFlag"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"渠道"}},[a("x-select",{attrs:{"show-default":"",goroup:"",options:e.channelOptions,url:"/channel/options"},model:{value:e.queryParams.channelId,callback:function(t){e.$set(e.queryParams,"channelId",t)},expression:"queryParams.channelId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:e.reload}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("el-row",[a("el-card",[a("el-col",{attrs:{span:24}},[a("x-radio",{staticStyle:{"margin-bottom":"10px","padding-left":"10px"},attrs:{size:"small","button-style":"solid",button:"",url:"/options/GetAnalysisTypes"},on:{change:function(t){return e.getLineData()}},model:{value:e.queryParams.analysisType,callback:function(t){e.$set(e.queryParams,"analysisType",t)},expression:"queryParams.analysisType"}}),e._v(" "),a("x-radio",{staticStyle:{"margin-bottom":"10px","padding-left":"10px"},attrs:{size:"small","button-style":"solid",button:"",url:"/options/GetTimeSpanTypes"},on:{change:function(t){return e.getLineData()}},model:{value:e.queryParams.timeSpanType,callback:function(t){e.$set(e.queryParams,"timeSpanType",t)},expression:"queryParams.timeSpanType"}}),e._v(" "),a("line-chart",{attrs:{"chart-data":e.installLineData,legentShow:!0,titleShow:!0,titleText:e.installLineData.title,interval:e.installLineData.interval}})],1)],1)],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("5a0c"),l=a.n(s),o=a("4624"),c=a("365c"),u=a("352b"),d={components:{LineChart:u["a"]},mixins:[o["a"]],data:function(){return{tableHeightOptions:{page:!1},queryParams:{appFlag:-1,channelId:-1,analysisType:0,timeSpanType:5,date:[]},loading:!1,channelOptions:[],installLineData:{}}},mounted:function(){this.reset(),this.getLineData()},methods:{queryReset:function(){this.queryParams={appFlag:-1,channelId:-1,analysisType:0,timeSpanType:5,date:[]},this.reset(),this.getLineData()},getLineData:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date[0],t.endTime=this.queryParams.date[1]),e.next=5,c["a"].getinstalllinedata(t);case 5:a=e.sent,0==a.code&&(this.installLineData=a.data||{}),this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),reset:function(){var e=l()().subtract(1,"day").format("YYYY-MM-DD"),t=l()().format("YYYY-MM-DD");this.queryParams.date=[e,t]},reload:function(){this.getLineData()},handleAppIdChange:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.queryParams.channelId=-1,e.next=3,c["f"].getOptions({appId:this.queryParams.appFlag});case 3:t=e.sent,0==t.code&&(this.channelOptions=t.data);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},p=d,m=a("2877"),f=Object(m["a"])(p,r,n,!1,null,null,null);t["default"]=f.exports},"99b0":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,width:"1200px"},on:{"update:visible":function(t){e.visible=t},cancel:function(t){e.visible=!1}}},[a("el-tabs",[a("el-tab-pane",{attrs:{label:"Entity.cs"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.entity,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.entity)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Table.md"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.tableMd,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.tableMd)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"ResponseVO.cs"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.responseVO,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.responseVO)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"RequestParams.cs"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.requestParams,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.requestParams)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"IRepository.cs"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.iRepository,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.iRepository)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Repository.cs"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.repository,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.repository)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"IService.cs"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.iService,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.iService)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Service.cs"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.service,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.service)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Controller.cs"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.controller,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.controller)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"api.js"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.api,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.api)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"index.vue"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.index,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.index)+" ")])],1),e._v(" "),a("el-tab-pane",{attrs:{label:"Edit.vue"}},[a("el-button",{staticClass:"copy-btn",attrs:{type:"text",icon:"el-icon-document-copy"},on:{click:function(t){return e.handleCopy(e.data.edit,t)}}},[e._v("复制代码")]),e._v(" "),a("pre",[e._v(" "+e._s(e.data.edit)+" ")])],1)],1)],1)},n=[],i=(a("a481"),a("3b2b"),a("96cf"),a("1da1")),s=a("2b0e"),l=a("b311"),o=a.n(l);function c(){s["default"].prototype.$message({message:"复制成功",type:"success",duration:1500})}function u(){s["default"].prototype.$message({message:"复制失败",type:"error"})}function d(e,t){var a=new o.a(t.target,{text:function(){return e}});a.on("success",(function(){c(),a.destroy()})),a.on("error",(function(){u(),a.destroy()})),a.onClick(t)}var p=a("365c"),m={data:function(){return{title:"预览",visible:!1,loading:!1,data:{}}},methods:{open:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.data={},this.visible=!0,this.$xloading.show(),e.next=5,p["genApi"].getCodePreview(t);case 5:a=e.sent,this.$xloading.hide(),this.data=a.data;case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),replaceEnter:function(e){var t=new RegExp("\r\n","g");return e.replace(t,"<br/>")},handleCopy:function(e,t){d(e,t)}}},f=m,h=(a("23c5"),a("2877")),g=Object(h["a"])(f,r,n,!1,null,"4dc2954c",null);t["default"]=g.exports},"9ed6":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"login"},[a("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules}},[a("h3",{staticClass:"title"},[e._v("渠道统计后台登录")]),e._v(" "),a("el-form-item",{attrs:{prop:"username"}},[a("el-input",{attrs:{type:"text","auto-complete":"off",placeholder:"账号"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}},[a("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"user"},slot:"prefix"})],1)],1),e._v(" "),a("el-form-item",{attrs:{prop:"password"}},[a("el-input",{attrs:{type:"password","auto-complete":"off",placeholder:"密码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}},[a("svg-icon",{staticClass:"el-input__icon input-icon",attrs:{slot:"prefix","icon-class":"password"},slot:"prefix"})],1)],1),e._v(" "),a("el-checkbox",{staticStyle:{margin:"0px 0px 25px 0px"},model:{value:e.loginForm.rememberMe,callback:function(t){e.$set(e.loginForm,"rememberMe",t)},expression:"loginForm.rememberMe"}},[e._v("记住我")]),e._v(" "),a("el-form-item",{staticClass:"login-btn",staticStyle:{width:"100%","margin-bottom":"10px"}},[a("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,size:"medium",type:"primary"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e.loading?a("span",[e._v("登 录 中...")]):a("span",[e._v("登 录")])])],1),e._v(" "),a("el-form-item",[a("el-button",{staticStyle:{width:"100%"},attrs:{size:"medium"},nativeOn:{click:function(t){return t.preventDefault(),e.handleOAuth(t)}}},[a("span",[e._v("用户中心登录")])])],1)],1),e._v(" "),e._m(0)],1)},n=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"el-login-footer"},[a("span")])}],i=(a("96cf"),a("1da1")),s=(a("a78e"),a("7d92")),l=a("5f87"),o=a("365c"),c={name:"Login",data:function(){var e=function(e,t,a){0==t.length?a(new Error("用户名不能为空")):a()},t=function(e,t,a){0==t.length?a(new Error("密码不能为空")):a()};return{loginForm:{username:"",password:""},loginRules:{username:[{required:!0,trigger:"blur",validator:e}],password:[{required:!0,trigger:"blur",validator:t}]},loading:!1,passwordType:"password",redirect:void 0}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},created:function(){this.getUserLoginParams()},methods:{getUserLoginParams:function(){var e=Object(l["d"])(),t=Object(l["a"])(),a=Object(l["b"])();this.loginForm={username:null===e?this.loginForm.username:e,password:null===t?this.loginForm.password:Object(s["a"])(t),rememberMe:null!==a&&Boolean(a)}},showPwd:function(){var e=this;"password"===this.passwordType?this.passwordType="":this.passwordType="password",this.$nextTick((function(){e.$refs.password.focus()}))},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){if(!t)return!1;e.$xloading.show();var a=Object.assign({},e.loginForm);e.loginForm.rememberMe?(Object(l["k"])(e.loginForm.username),Object(l["h"])(Object(s["b"])(e.loginForm.password)),Object(l["i"])(e.loginForm.rememberMe)):Object(l["g"])(),a.password=Object(s["c"])(a.password),e.$store.dispatch("Login",a).then((function(){e.$router.push({path:e.redirect||"/"}),e.$xloading.hide(),window.location.reload()})).catch((function(t){e.$xloading.hide(),e.$xMsgError(t)}))}))},handleOAuth:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,a,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.$loading(),e.next=3,o["e"].getYYAuthUrl();case 3:a=e.sent,t.close(),0==a.code?(r=a.data,window.location.href=r):this.$xMsgError(a.msg);case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},u=c,d=(a("254e"),a("2877")),p=Object(d["a"])(u,r,n,!1,null,null,null);t["default"]=p.exports},a078:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"medium"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"id",prop:"id"}},[a("el-input",{attrs:{readonly:!0,placeholder:"请输入Id"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"账号",prop:"userName"}},[a("el-input",{attrs:{placeholder:"请输入账号"},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"名称",prop:"nickName"}},[a("el-input",{attrs:{placeholder:"请输入名称"},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"密码",prop:"password"}},[a("el-input",{attrs:{placeholder:"请输入密码"},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"扣量率",prop:"lossRate"}},[a("el-input",{attrs:{placeholder:"请输入扣量率",type:"number"},model:{value:e.form.lossRate,callback:function(t){e.$set(e.form,"lossRate",t)},expression:"form.lossRate"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"账号渠道",prop:"channelIdList"}},[a("x-checkbox",{attrs:{goroup:"",url:"/channel/options"},model:{value:e.form.channelIdList,callback:function(t){e.$set(e.form,"channelIdList",t)},expression:"form.channelIdList"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"状态",prop:"enable"}},[a("el-radio-group",{model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}},[a("el-radio",{attrs:{label:!0}},[e._v("启用")]),e._v(" "),a("el-radio",{attrs:{label:!1}},[e._v("禁用")])],1)],1)],1)],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("365c"),l={data:function(){return{title:"",visible:!1,loading:!1,form:{statue:!0,channelIdList:[]},channelOptions:[]}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,s["g"].getDetail(t.id);case 6:a=e.sent,0==a.code&&(this.form=a.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={statue:!0,channelIdList:[]},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r,n,i,l;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=17;break}if(e.$xloading.show(),r=Object.assign({},e.form),n=r.id,void 0==n){t.next=12;break}return t.next=7,s["g"].editchannelUser(r);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,s["g"].addchannelUser(r);case 14:l=t.sent,e.$xloading.hide(),0==l.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+l.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},o=l,c=a("2877"),u=Object(c["a"])(o,r,n,!1,null,null,null);t["default"]=u.exports},a36b:function(e,t,a){e.exports=a.p+"static/img/404.a57b6f31.png"},a457:function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),a.d(t,"b",(function(){return i}));var r=a("b775");function n(e){return Object(r["a"])({url:"/Log/LoginLog",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/Log/OperLog",method:"get",params:e})}},a6f9:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"所属项目"}},[a("x-select",{attrs:{"show-default":"",customRender:"",url:"/appKeyConfig/options"},model:{value:e.queryParams.appId,callback:function(t){e.$set(e.queryParams,"appId",t)},expression:"queryParams.appId"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["channel:add"],expression:"['channel:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("新增")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["channel:edit"],expression:"['channel:edit']"}],attrs:{size:"mini",type:"warning",icon:"el-icon-edit",disabled:0===e.selectedRows.length},on:{click:e.handleBatchEdit}},[e._v("批量修改")])],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",align:"center",width:"55"}}),e._v(" "),a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"id",label:"渠道Id",align:"center",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appName",label:"项目名称",align:"center","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"渠道名称",align:"center","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"enable",label:"可用",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.enable?a("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("否")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"isDefault",label:"默认渠道",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.isDefault?a("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("否")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"createOn",label:"添加时间",align:"center",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"250",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["channel:downloadApk"],expression:"['channel:downloadApk']"}],attrs:{size:"mini",type:"info"},on:{click:function(a){return e.handleDownloadApk(t.row)}}},[e._v("下载")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["channel:edit"],expression:"['channel:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(a){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["channel:delete"],expression:"['channel:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}}),e._v(" "),a("download-dialog",{ref:"downloadDialog"}),e._v(" "),a("batch-edit-dialog",{ref:"batchEditDialog",on:{ok:e.handleOk}})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("f645"),l=a("4624"),o=a("365c"),c=a("eae9"),u=a("b5fa"),d=a("1223"),p={components:{EditDialog:c["default"],DownloadDialog:u["default"],BatchEditDialog:d["default"]},mixins:[l["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{},apkDlownloadHost:s["a"].apkDlownloadHost,selectedRows:[]}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,o["f"].getList(t);case 4:a=e.sent,this.tableData=a,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,o["f"].delChannel(t.id);case 3:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("删除成功")):a.$xMsgError("删除失败！"+r.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleSelectionChange:function(e){this.selectedRows=e.filter((function(e){return 0==e.isDefault}))},handleBatchEdit:function(){0!==this.selectedRows.length?this.$refs.batchEditDialog.open(this.selectedRows):this.$xMsgWarning("请先选择要修改的数据")},handleDownloadApk:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a,r,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=t.appFlag,e.t0=t.appFlag,e.next="kkz"===e.t0?4:6;break;case 4:return a="gamebox",e.abrupt("break",6);case 6:r=this.apkDlownloadHost+"?p="+a+"&cid="+t.id,n={downloadUrl:r},this.$refs.downloadDialog.show(t,n);case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},m=p,f=a("2877"),h=Object(f["a"])(m,r,n,!1,null,null,null);t["default"]=h.exports},ac0d:function(e,t,a){"use strict";a.d(t,"a",(function(){return l}));a("8e6e"),a("ac6a"),a("456d");var r=a("ade3"),n=a("2f62");a("d0a2"),a("de11");function i(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),a.push.apply(a,r)}return a}function s(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?i(Object(a),!0).forEach((function(t){Object(r["a"])(e,t,a[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):i(Object(a)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))}))}return e}var l={computed:s({},Object(n["d"])({device:function(e){return e.app.device}})),mounted:function(){this.noFitEditDialogWidth||(this.isMobile()?this.dialogWidth="100%":this.dialogWidth="800px")},methods:{isMobile:function(){return"mobile"==this.device},isDesktop:function(){return"desktop"==this.device}}}},af40:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{"label-width":"80px",inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"所属项目"}},[a("x-select",{attrs:{"show-default":"",customRender:"",url:"/appKeyConfig/options"},on:{change:e.handleAppIdChange},model:{value:e.queryParams.appId,callback:function(t){e.$set(e.queryParams,"appId",t)},expression:"queryParams.appId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"渠道"}},[a("x-select",{attrs:{"show-default":"",goroup:"",options:e.channelOptions,url:"/channel/options"},model:{value:e.queryParams.channelId,callback:function(t){e.$set(e.queryParams,"channelId",t)},expression:"queryParams.channelId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"date",label:"日期",align:"center",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"install",label:"排重安装数",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"installNoCheck",label:"真实安装",align:"center","min-width":"150"},scopedSlots:e._u([{key:"header",fn:function(){return[a("x-table-header-tip",{attrs:{label:"真实安装数",tip:"不排重安装"}})]},proxy:!0}])}),e._v(" "),a("el-table-column",{attrs:{prop:"launchUserOld",label:"留存启动用户数",align:"center","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"activeUserCount",label:"活跃用户数",align:"center","min-width":"150"},scopedSlots:e._u([{key:"header",fn:function(){return[a("x-table-header-tip",{attrs:{label:"活跃用户数",tip:"活跃用户数=排重安装数+留存启动用户数"}})]},proxy:!0}])}),e._v(" "),a("el-table-column",{attrs:{prop:"launchNew",label:"新用户启动次数",align:"center","min-width":"150"},scopedSlots:e._u([{key:"header",fn:function(){return[a("x-table-header-tip",{attrs:{label:"新用户启动次数",tip:"当天安装的用户启动的总次数"}})]},proxy:!0}])}),e._v(" "),a("el-table-column",{attrs:{prop:"launchOld",label:"留存启动次数",align:"center","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"exitCount",label:"退出次数",align:"center","min-width":"150"}})],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("5a0c"),l=a.n(s),o=a("4624"),c=a("365c"),u={mixins:[o["a"]],data:function(){return{queryParams:{appId:-1,channelId:-1},loading:!1,tableData:{},channelOptions:[]}},created:function(){this.reset()},methods:{queryReset:function(){this.queryParams={appId:-1,channelId:-1},this.reset(),this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date[0],t.endTime=this.queryParams.date[1]),e.next=5,c["n"].getChannelStatsList(t);case 5:a=e.sent,this.tableData=a,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){var e=l()().subtract(10,"day").format("YYYY-MM-DD"),t=l()().format("YYYY-MM-DD");this.queryParams.date=[e,t]},handleAppIdChange:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.queryParams.channelId=-1,e.next=3,c["f"].getOptions({appId:this.queryParams.appId});case 3:t=e.sent,0==t.code&&(this.channelOptions=t.data);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},d=u,p=a("2877"),m=Object(p["a"])(d,r,n,!1,null,null,null);t["default"]=m.exports},b157:function(e,t,a){},b5fa:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{attrs:{title:e.title,visible:e.visible,"show-footer":!0},on:{"update:visible":function(t){e.visible=t},cancel:e.close}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"150px",size:"medium"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"所属项目："}},[e._v("\n          "+e._s(e.channel.appName)+"\n        ")])],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"渠道名称："}},[e._v("\n          "+e._s(e.channel.name)+"\n        ")])],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"渠道Id："}},[e._v("\n          "+e._s(e.channel.id)+"\n        ")])],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"下载链接："}},[a("el-link",{attrs:{href:e.info.downloadUrl,type:"primary",underline:!1,download:""}},[e._v(e._s(e.info.downloadUrl))])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"扫码下载："}},[a("vue-qr",{attrs:{text:e.info.downloadUrl}})],1)],1)],1)],1),e._v(" "),a("template",{slot:"footer"},[a("el-button",{attrs:{size:"medium"},on:{click:e.close}},[e._v("关闭")])],1)],2)},n=[],i=a("658f"),s=a.n(i),l={components:{vueQr:s.a},data:function(){return{title:"",visible:!1,loading:!1,form:{},channel:{},info:{},downloadStatus:void 0,downloadPercentage:0,showProgress:!1}},methods:{show:function(e,t){this.reset(),this.channel=e,this.info=t,this.title="下载渠道包",this.visible=!0},close:function(){this.reset(),this.visible=!1},reset:function(){this.form={},this.$xResetForm("form"),this.loading=!1,this.showProgress=!1}}},o=l,c=a("2877"),u=Object(c["a"])(o,r,n,!1,null,null,null);t["default"]=u.exports},bb53:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[a("el-row",[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"登录账号",prop:"userName"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.form.userName,callback:function(t){e.$set(e.form,"userName",t)},expression:"form.userName"}})],1)],1),e._v(" "),e.isEdit?e._e():a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"密码",prop:"password"}},[a("el-input",{attrs:{placeholder:"","show-password":"",autocomplete:"new-password"},model:{value:e.form.password,callback:function(t){e.$set(e.form,"password",t)},expression:"form.password"}})],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"姓名",prop:"nickName"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.form.nickName,callback:function(t){e.$set(e.form,"nickName",t)},expression:"form.nickName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"用户中心ID",prop:"authUId"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.form.authUId,callback:function(t){e.$set(e.form,"authUId",t)},expression:"form.authUId"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"角色",prop:"roleIds"}},[a("x-checkbox",{attrs:{url:"/system/user/roleList",map:e.roleOptionsMap},model:{value:e.form.roleIds,callback:function(t){e.$set(e.form,"roleIds",t)},expression:"form.roleIds"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[a("el-radio",{attrs:{label:!0}},[e._v("启用")]),e._v(" "),a("el-radio",{attrs:{label:!1}},[e._v("禁用")])],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1)],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("365c"),l={data:function(){return{isEdit:!1,title:"",visible:!1,loading:!1,form:{},rules:{userName:[{required:!0,message:"登录账号不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"},{min:6,max:16,message:"长度在 6 到 16 个字符",trigger:"blur"}],nickName:[{required:!0,message:"姓名不能为空",trigger:"blur"}],roleIds:[{required:!0,message:"角色不能为空",trigger:"blur"}],systemCodes:[{required:!0,message:"系统权限不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"blur"}]},roleOptions:[]}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})));function t(){return e.apply(this,arguments)}return t}(),methods:{add:function(){this.isEdit=!1,this.reset(),this.title="新增",this.visible=!0,this.getRoleOptions()},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a,r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.isEdit=!0,this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=7,this.getRoleOptions();case 7:return e.next=9,s["q"].getDetail(t.id);case 9:a=e.sent,0==a.code&&(this.$xloading.hide(),this.form=a.data,r=a.data.authUId,0==r&&(this.form.authUId=void 0));case 11:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){this.form={id:void 0,title:void 0,roleIds:[],systemCodes:[]},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r,n,i,l;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=17;break}if(e.$xloading.show(),r=Object.assign({},e.form),n=r.id,void 0==n){t.next=12;break}return t.next=7,s["q"].editUser(r);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,s["q"].addUser(r);case 14:l=t.sent,e.$xloading.hide(),0==l.code?(e.$xMsgSuccess("添加成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+l.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},roleOptionsMap:function(e){return e.map((function(e){return{label:e.roleName,value:e.id}}))},getRoleOptions:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,s["q"].getRoleList();case 2:t=e.sent,0==t.code&&(this.roleOptions=t.data.map((function(e){return{label:e.roleName,value:e.id}})));case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},o=l,c=a("2877"),u=Object(c["a"])(o,r,n,!1,null,null,null);t["default"]=u.exports},c641:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible,"destroy-on-close":""},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:function(t){e.visible=!1}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"角色名称",prop:"roleName"}},[a("el-input",{attrs:{placeholder:""},model:{value:e.form.roleName,callback:function(t){e.$set(e.form,"roleName",t)},expression:"form.roleName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"是否启用",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[a("el-radio",{attrs:{label:!0}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"排序",prop:"sort",required:""}},[a("el-input-number",{attrs:{"controls-position":"right",placeholder:""},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",rows:2,placeholder:""},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:20,offset:2}},[a("el-card",{scopedSlots:e._u([{key:"header",fn:function(){},proxy:!0}])},[e._v(" "),a("div",[a("el-form-item",{attrs:{label:"菜单权限",prop:"menuIds"}},[a("el-tree",{ref:"menuTree1",attrs:{data:e.menuTree1,"show-checkbox":"","check-strictly":"","node-key":"id",props:{label:"title",children:"children"}}})],1)],1)])],1)],1)],1)],1)},n=[],i=a("2909"),s=(a("96cf"),a("1da1")),l=a("3528"),o={data:function(){return{title:"",visible:!1,loading:!1,form:{},rules:{roleName:[{required:!0,message:"角色名称不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"blur"}],sort:[{required:!0,message:"排序不能为空",trigger:"blur"}]},menuTree1:[]}},methods:{add:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.reset(),this.getMenuTreeData(),this.title="新增",this.visible=!0;case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),edit:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,this.getMenuTreeData();case 6:return e.next=8,l["e"](t.id);case 8:a=e.sent,0==a.code&&(this.$xloading.hide(),this.form=a.data,void 0!=this.$refs.menuTree1&&this.$refs.menuTree1.setCheckedKeys(a.data.menuIds));case 10:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.form.menuIds=Object(i["a"])(this.$refs.menuTree1.getCheckedKeys()),this.$refs["form"].validate(function(){var t=Object(s["a"])(regeneratorRuntime.mark((function t(a){var r,n,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=17;break}if(e.$xloading.show(),r=e.form,n=e.form.id,void 0==n){t.next=12;break}return t.next=7,l["d"](r);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,l["a"](r);case 14:s=t.sent,e.$xloading.hide(),0==s.code?(e.$xMsgSuccess("添加成功"),e.visible=!1,e.$emit("ok")):e.$xMsgError("操作失败！"+s.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},getMenuTreeData:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l["g"](0);case 2:t=e.sent,0==t.code&&(this.menuTree1=t.data);case 4:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},c=o,u=a("2877"),d=Object(u["a"])(c,r,n,!1,null,null,null);t["default"]=d.exports},c89d:function(e,t,a){"use strict";a("7fe1")},cccda:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["appKeyConfig:add"],expression:"['appKeyConfig:add']"}],attrs:{size:"mini",type:"primary",icon:"el-icon-plus"},on:{click:e.handleAdd}},[e._v("添加项目")])],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appName",label:"项目名称",align:"center",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appFlag",label:"项目标识",align:"center",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"dsProjectType",title:"DS标识",align:"center",width:100}}),e._v(" "),a("el-table-column",{attrs:{prop:"appPkgName",label:"App包名",align:"center",width:"300"}}),e._v(" "),a("el-table-column",{attrs:{prop:"isActive",label:"线上项目",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.isActive?a("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("否")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"isUpgrade",label:"开启升级",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.isUpgrade?a("el-tag",{attrs:{type:"success",size:"medium"}},[e._v("是")]):a("el-tag",{attrs:{type:"danger",size:"medium"}},[e._v("否")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"appKey",label:"项目凭证",align:"center","min-width":"300"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createOn",label:"添加时间",align:"center",width:"160"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作","min-width":"280",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["appUpgradeConfig:list"],expression:"['appUpgradeConfig:list']"}],attrs:{size:"mini",type:"primary"},on:{click:function(a){return e.handleUpgradeList(t.row)}}},[e._v("升级列表")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["appKeyConfig:edit"],expression:"['appKeyConfig:edit']"}],attrs:{size:"mini",type:"warning"},on:{click:function(a){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["appKeyConfig:delete"],expression:"['appKeyConfig:delete']"}],attrs:{size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("4624"),l=a("365c"),o=a("eb73"),c={components:{EditDialog:o["default"]},mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{}}},methods:{queryReset:function(){this.queryParams={},this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),e.next=4,l["b"].getList(t);case 4:a=e.sent,this.tableData=a,this.$xloading.hide();case 7:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then(Object(i["a"])(regeneratorRuntime.mark((function e(){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a.$xloading.show(),e.next=3,l["b"].delAppKeyConfig(t.id);case 3:r=e.sent,a.$xloading.hide(),0==r.code?(a.$refs.table.refresh(),a.$xMsgSuccess("删除成功")):a.$xMsgError("删除失败！"+r.msg);case 6:case"end":return e.stop()}}),e)})))).catch((function(){a.$xloading.hide()}));case 1:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleOk:function(){this.$refs.table.refresh()},handleUpgradeList:function(e){this.$router.push({path:"/upgradeConfig",query:{appKey:e.appKey}})}}},u=c,d=a("2877"),p=Object(d["a"])(u,r,n,!1,null,null,null);t["default"]=p.exports},d0a2:function(e,t,a){"use strict";a("f559"),a("ac6a");var r=a("ed08"),n={data:function(){return{tableHeight:0,isMobile:!1}},mounted:function(){var e=this;this.isMobile=Object(r["b"])();var t=function(t){var a={queryName:"queryForm",toolbarName:"toolbar",page:!0,refs:[],value:0};t=Object.assign({},a,t);var r=50,n=40,i=2,s=40,l=0;e.$refs[t.queryName]&&(l=e.$refs[t.queryName].$el.offsetHeight);var o=0;e.$refs[t.toolbarName]&&(o=e.$refs[t.toolbarName].$el.offsetHeight+15);var c=0;t.page&&(c=32);var u=0;if(t.refs.forEach((function(t){var a="$el-";t.startsWith(a)?u+=e.$refs[t.substring(a.length)].$el.offsetHeight:u+=e.$refs[t].$el.offsetHeight})),!e.isMobile){var d=window.innerHeight-(r+n+s+i+l+o+c+1+t.value+u);e.tableHeight=d}};window.onresize=function(){t(e.tableHeightOptions)},this.$nextTick((function(){t(e.tableHeightOptions)}))}};t["a"]=n},d933:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{"label-width":"80px",inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"所属项目"}},[a("x-select",{attrs:{"show-default":"",customRender:"",url:"/appKeyConfig/flagOptions"},model:{value:e.queryParams.appFlag,callback:function(t){e.$set(e.queryParams,"appFlag",t)},expression:"queryParams.appFlag"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"date","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"安卓Id"}},[a("el-input",{model:{value:e.queryParams.deviceId,callback:function(t){e.$set(e.queryParams,"deviceId",t)},expression:"queryParams.deviceId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"渠道Id"}},[a("el-input",{model:{value:e.queryParams.channelId,callback:function(t){e.$set(e.queryParams,"channelId",t)},expression:"queryParams.channelId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"锚点"}},[a("x-select",{attrs:{options:e.symbolOptions,url:"/symbolConfig/options"},model:{value:e.queryParams.symbol,callback:function(t){e.$set(e.queryParams,"symbol",t)},expression:"queryParams.symbol"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"symbol",label:"锚点标识",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"symbolName",label:"锚点名称",align:"center","min-width":"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"value",label:"锚点值",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"channelId",label:"渠道Id",align:"center",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"packCId",label:"包渠道Id",align:"center",width:"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"channelName",label:"渠道名称",align:"center",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"appFlag",label:"App标识",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"deviceId",label:"安卓Id",align:"center",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"brand",label:"品牌",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"model",label:"型号",align:"center","min-width":"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"screen",label:"屏幕",align:"center","min-width":"100"}}),e._v(" "),a("el-table-column",{attrs:{prop:"createOn",label:"时间",align:"center",width:"180"}})],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("5a0c"),l=a.n(s),o=a("4624"),c=a("365c"),u={mixins:[o["a"]],data:function(){return{queryParams:{appFlag:-1,runType:-1,date:""},loading:!1,tableData:{},symbolOptions:[]}},created:function(){this.reset()},methods:{queryReset:function(){this.queryParams={appFlag:-1,runType:-1,date:l()().format("YYYY-MM-DD")},this.$refs.table.refresh(!0)},reset:function(){var e=l()().format("YYYY-MM-DD");this.queryParams.date=e},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date),e.next=5,c["p"].getList(t);case 5:a=e.sent,this.tableData=a,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),handleAdd:function(){this.$refs.editDialog.add()},handleEdit:function(e){this.$refs.editDialog.edit(e)}}},d=u,p=a("2877"),m=Object(p["a"])(d,r,n,!1,null,null,null);t["default"]=m.exports},db6a:function(e,t,a){"use strict";a("32d1")},de11:function(e,t,a){"use strict";a("a481"),a("386d"),a("4328"),a("83d6")},e38b:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{inline:"","label-width":"80px"}},[a("el-row",{attrs:{gutter:15}},[a("el-form-item",{attrs:{label:"标题"}},[a("el-input",{model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"操作人员"}},[a("el-input",{model:{value:e.queryParams.operator,callback:function(t){e.$set(e.queryParams,"operator",t)},expression:"queryParams.operator"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{attrs:{placeholder:""},model:{value:e.queryParams.operateType,callback:function(t){e.$set(e.queryParams,"operateType",t)},expression:"queryParams.operateType"}},e._l(e.operateTypeOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{attrs:{placeholder:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"daterange",placeholder:"","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:function(){return e.queryParams={}}}},[e._v("重置")])],1)],1)],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"logId",label:"ID",align:"center",width:"80"}}),e._v(" "),a("el-table-column",{attrs:{prop:"name",label:"标题",align:"center",width:"200"}}),e._v(" "),a("el-table-column",{attrs:{prop:"operateType",label:"操作类型",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.operateType==e.operateType.Add?a("el-tag",{attrs:{type:"success"}},[e._v("新增")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Edit?a("el-tag",{attrs:{type:"warning"}},[e._v("修改")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Delete?a("el-tag",{attrs:{type:"danger"}},[e._v("删除")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Query?a("el-tag",[e._v("查询")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Unknown?a("el-tag",{attrs:{type:"info"}},[e._v("其它")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Login?a("el-tag",{attrs:{color:"#0070d3",effect:"dark"}},[e._v("登录")]):e._e(),e._v(" "),t.row.operateType==e.operateType.Logout?a("el-tag",{attrs:{color:"#ec0810",effect:"dark"}},[e._v("注销")]):e._e()]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"tableName",label:"表名",align:"center",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.tableName?a("span",[e._v(e._s(t.row.tableName))]):a("span",[e._v(" - ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"tableId",label:"ID",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.tableId?a("span",[e._v(e._s(t.row.tableId))]):a("span",[e._v(" - ")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"desc",label:"描述",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.desc?a("span",[e._v(e._s(t.row.desc))]):a("span",[e._v("-")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"ip",label:"IP",align:"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(e._s(t.row.ip))]),e._v(" "),a("div",[e._v(e._s(t.row.ipLocation))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"status",label:"状态",align:"center",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"==t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("成功")]):"1"==t.row.status?a("el-tag",{attrs:{type:"warning"}},[e._v("失败")]):a("el-tag",{attrs:{type:"danger"}},[e._v("异常")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"operatorName",label:"操作人员",align:"center",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(e._s(t.row.operatorName))]),e._v(" "),a("div",[e._v("ID: "+e._s(t.row.operatorId))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"logTime",label:"操作时间",align:"center",width:"180"}}),e._v(" "),a("el-table-column",{attrs:{label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("详细")])]}}])})],1)],1),e._v(" "),a("detail-dialog",{ref:"detailDialog"})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("4624"),l=a("60fe"),o=a("55cb"),c=a("a457"),u={mixins:[s["a"]],components:{DetailDialog:o["default"]},data:function(){return{searchCol:{xs:24,md:8},queryParams:{},loading:!1,tableData:{},operateTypeOptions:[{label:"新增",value:1},{label:"修改",value:2},{label:"删除",value:3},{label:"查询",value:4},{label:"注销",value:5},{label:"登录",value:6},{label:"其它",value:9}],statusOptions:[{label:"成功",value:"0"},{label:"失败",value:"1"},{label:"异常",value:"2"}],operateType:l["OperateType"]}},methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),t.date&&(t.beginTime=t.date[0],t.endTime=t.date[1]),e.next=5,c["b"](t);case 5:a=e.sent,this.tableData=a,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),renderOperateType:function(e){var t=e.operateType,a="未知";switch(t){case"1":a="新增";break;case"2":a="修改";break;case"3":a="删除";break;case"4":a="查询";break;case"5":a="退出登录";break;default:}return a},handleView:function(e){this.$refs.detailDialog.show(e)}}},d=u,p=a("2877"),m=Object(p["a"])(d,r,n,!1,null,null,null);t["default"]=m.exports},e702:function(e,t,a){},eae9:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"渠道名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入渠道名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"所属项目",prop:"appId"}},[a("x-select",{attrs:{customRender:"",url:"/appKeyConfig/options"},model:{value:e.form.appId,callback:function(t){e.$set(e.form,"appId",t)},expression:"form.appId"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"是否可用",prop:"enable"}},[a("el-radio-group",{model:{value:e.form.enable,callback:function(t){e.$set(e.form,"enable",t)},expression:"form.enable"}},[a("el-radio",{attrs:{label:!0}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:!1}},[e._v("否")])],1)],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"是否默认",prop:"isDefault"}},[a("el-radio-group",{model:{value:e.form.isDefault,callback:function(t){e.$set(e.form,"isDefault",t)},expression:"form.isDefault"}},[a("el-radio",{attrs:{label:!0}},[e._v("是")]),e._v(" "),a("el-radio",{attrs:{label:!1}},[e._v("否")])],1),e._v(" "),a("span",{staticClass:"extra"},[e._v("没有渠道的ID的数据会统计入默认渠道中")])],1)],1)],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("365c"),l={data:function(){return{title:"",visible:!1,loading:!1,form:{},rules:{name:[{required:!0,message:"请输入渠道名称",trigger:"blur"}],projectId:[{required:!0,message:"请选择所属项目",trigger:"blur"}],enable:[{required:!0,message:"请选择是否可用",trigger:"blur"}],isActive:[{required:!0,message:"请选择是否线上项目",trigger:"blur"}],isDefault:[{required:!0,message:"请选择是否默认渠道",trigger:"blur"}]}}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,s["f"].getDetail(t.id);case 6:a=e.sent,0==a.code&&(this.form=a.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r,n,i,l;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=17;break}if(e.$xloading.show(),r=Object.assign({},e.form),n=r.id,void 0==n){t.next=12;break}return t.next=7,s["f"].editChannel(r);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,s["f"].addChannel(r);case 14:l=t.sent,e.$xloading.hide(),0==l.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+l.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},o=l,c=a("2877"),u=Object(c["a"])(o,r,n,!1,null,null,null);t["default"]=u.exports},eb73:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[a("el-row",[e.isEdit?a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"项目凭证",prop:"appKey"}},[a("el-input",{attrs:{placeholder:"请输入项目凭证",disabled:""},model:{value:e.form.appKey,callback:function(t){e.$set(e.form,"appKey",t)},expression:"form.appKey"}})],1)],1):e._e(),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"项目名称",prop:"appName"}},[a("el-input",{attrs:{placeholder:"请输入App项目名称"},model:{value:e.form.appName,callback:function(t){e.$set(e.form,"appName",t)},expression:"form.appName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"项目标识",prop:"appFlag"}},[a("el-input",{attrs:{placeholder:"请输入项目标识"},model:{value:e.form.appFlag,callback:function(t){e.$set(e.form,"appFlag",t)},expression:"form.appFlag"}}),e._v(" "),a("span",{staticClass:"extra"},[e._v("只允许输入英文、数字，一般为项目拼音首字母")])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"DS标识",prop:"dsProjectType"}},[a("el-input-number",{attrs:{placeholder:"请输入DS标识"},model:{value:e.form.dsProjectType,callback:function(t){e.$set(e.form,"dsProjectType",t)},expression:"form.dsProjectType"}}),e._v(" "),a("span",{staticClass:"extra"},[e._v("对应App的平台ProjectType")])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"App包名",prop:"appPkgName"}},[a("el-input",{attrs:{placeholder:"请输入App包名"},model:{value:e.form.appPkgName,callback:function(t){e.$set(e.form,"appPkgName",t)},expression:"form.appPkgName"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"项目秘钥",prop:"appEncryptKey"}},[a("el-input",{attrs:{placeholder:"请输入加解密秘钥字符串"},model:{value:e.form.appEncryptKey,callback:function(t){e.$set(e.form,"appEncryptKey",t)},expression:"form.appEncryptKey"}}),e._v(" "),a("span",{staticClass:"extra"},[e._v("不填写将会自动创建")])],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"是否线上项目",prop:"isActive"}},[a("el-radio-group",{model:{value:e.form.isActive,callback:function(t){e.$set(e.form,"isActive",t)},expression:"form.isActive"}},[a("el-radio-button",{attrs:{label:!0}},[e._v("是")]),e._v(" "),a("el-radio-button",{attrs:{label:!1}},[e._v("否")])],1)],1)],1)],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("365c"),l={data:function(){return{title:"",visible:!1,loading:!1,form:{},isEdit:!1,rules:{appName:[{required:!0,message:"请输入App项目名称",trigger:"blur"}],appFlag:[{required:!0,message:"请输入项目标识",trigger:"blur"}],dsProjectType:[{required:!0,message:"请输入App标识",trigger:"blur"}]}}},methods:{add:function(){this.isEdit=!1,this.reset(),this.title="添加项目",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.isEdit=!0,this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=7,s["b"].getDetail(t.id);case 7:a=e.sent,0==a.code&&(this.form=a.data),this.$xloading.hide();case 10:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r,n,i,l;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=17;break}if(e.$xloading.show(),r=Object.assign({},e.form),n=r.id,void 0==n){t.next=12;break}return t.next=7,s["b"].editAppKeyConfig(r);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,s["b"].addAppKeyConfig(r);case 14:l=t.sent,e.$xloading.hide(),0==l.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+l.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},o=l,c=a("2877"),u=Object(c["a"])(o,r,n,!1,null,null,null);t["default"]=u.exports},ec0e:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("x-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{title:e.title,visible:e.visible},on:{"update:visible":function(t){e.visible=t},submit:e.submitForm,cancel:e.close}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px",size:"medium"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"id",prop:"id"}},[a("el-input",{attrs:{readonly:!0,placeholder:"请输入Id"},model:{value:e.form.id,callback:function(t){e.$set(e.form,"id",t)},expression:"form.id"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"锚点标识",prop:"symbol"}},[a("el-input",{attrs:{placeholder:"请输入锚点标识"},model:{value:e.form.symbol,callback:function(t){e.$set(e.form,"symbol",t)},expression:"form.symbol"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"锚点名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入锚点名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"值类型",prop:"type"}},[a("x-select",{attrs:{url:"/options/getSymbolTypes"},model:{value:e.form.type,callback:function(t){e.$set(e.form,"type",t)},expression:"form.type"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"描述",prop:"dest"}},[a("el-input",{attrs:{type:"textarea",rows:"5",placeholder:"请输入描述"},model:{value:e.form.dest,callback:function(t){e.$set(e.form,"dest",t)},expression:"form.dest"}})],1)],1)],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("365c"),l={data:function(){return{title:"",visible:!1,loading:!1,form:{},rules:{symbol:[{required:!0,message:"请输入锚点标识",trigger:"blur"}],name:[{required:!0,message:"请输入锚点名称",trigger:"blur"}]}}},methods:{add:function(){this.reset(),this.title="新增",this.visible=!0},edit:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.reset(),this.title="修改",this.visible=!0,this.$xloading.show(),e.next=6,s["o"].getDetail(t.id);case 6:a=e.sent,0==a.code&&(this.form=a.data),this.$xloading.hide();case 9:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),close:function(){this.reset(),this.visible=!1},reset:function(){this.form={},this.$xResetForm("form")},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r,n,i,l;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=17;break}if(e.$xloading.show(),r=Object.assign({},e.form),n=r.id,void 0==n){t.next=12;break}return t.next=7,s["o"].editsymbolConfig(r);case 7:i=t.sent,e.$xloading.hide(),0==i.code?(e.$xMsgSuccess("修改成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+i.msg),t.next=17;break;case 12:return t.next=14,s["o"].addsymbolConfig(r);case 14:l=t.sent,e.$xloading.hide(),0==l.code?(e.$xMsgSuccess("添加成功"),e.close(),e.$emit("ok")):e.$xMsgError("操作失败！"+l.msg);case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())}}},o=l,c=a("2877"),u=Object(c["a"])(o,r,n,!1,null,null,null);t["default"]=u.exports},ecd0:function(e,t,a){},eeda:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{"label-width":"80px",inline:"",size:"mini"}},[a("el-form-item",{attrs:{label:"所属项目"}},[a("x-select",{attrs:{"show-default":"",customRender:"",url:"/appKeyConfig/options"},on:{change:e.handleAppIdChange},model:{value:e.queryParams.appId,callback:function(t){e.$set(e.queryParams,"appId",t)},expression:"queryParams.appId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"渠道"}},[a("x-select",{attrs:{"show-default":"",goroup:"",options:e.channelOptions,url:"/channel/options"},model:{value:e.queryParams.channelId,callback:function(t){e.$set(e.queryParams,"channelId",t)},expression:"queryParams.channelId"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"锚点"}},[a("x-select",{attrs:{options:e.symbolOptions,url:"/symbolConfig/options"},model:{value:e.queryParams.symbol,callback:function(t){e.$set(e.queryParams,"symbol",t)},expression:"queryParams.symbol"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh"},on:{click:e.queryReset}},[e._v("重置")])],1)],1),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight,"cell-style":e.handleCellStyle}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),e._v(" "),a("el-table-column",{attrs:{prop:"date",label:"日期",align:"center",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"day0",label:"锚点值",align:"center","min-width":"100"}}),e._v(" "),a("day-table-column",{attrs:{day:1}}),e._v(" "),a("day-table-column",{attrs:{day:2}}),e._v(" "),a("day-table-column",{attrs:{day:3}}),e._v(" "),a("day-table-column",{attrs:{day:4}}),e._v(" "),a("day-table-column",{attrs:{day:5}}),e._v(" "),a("day-table-column",{attrs:{day:6}}),e._v(" "),a("day-table-column",{attrs:{day:7}})],1)],1)],1)},n=[],i=(a("4917"),a("96cf"),a("1da1")),s=a("5a0c"),l=a.n(s),o=a("4624"),c=a("365c"),u=a("31fa"),d={components:{DayTableColumn:u["default"]},mixins:[o["a"]],data:function(){return{queryParams:{appId:-1,channelId:-1,symbol:""},loading:!1,tableData:{},channelOptions:[],symbolOptions:[]}},created:function(){this.reset()},methods:{queryReset:function(){this.queryParams={appId:-1,channelId:-1,symbol:""},this.reset(),this.$refs.table.refresh(!0)},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),this.queryParams.date&&(t.beginTime=this.queryParams.date[0],t.endTime=this.queryParams.date[1]),e.next=5,c["n"].getChannelCustomRetainList(t);case 5:a=e.sent,this.tableData=a,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),reset:function(){var e=l()().subtract(10,"day").format("YYYY-MM-DD"),t=l()().format("YYYY-MM-DD");this.queryParams.date=[e,t]},handleCellStyle:function(e){var t=e.row,a=e.column,r=(e.rowIndex,e.columnIndex,a.label),n=/^[1-9]+/g,i=r.match(n);if(i){var s=i[0],l=this.isDayShow(t,s);if(l){var o=null,c=t["day".concat(s,"_Percent")];if(0!=c)return o=c<10?"#e6fcf5":c<20?"#c3fae8":c<30?"#96f2d7":c<50?"#63e6be":c<60?"#38d9a9":"#20c997",o?"background: ".concat(o,";"):void 0}}},isDayShow:function(e,t){var a=l()(e.date),r=a.add(t,"day"),n=l()();return!r.isAfter(n,"day")},handleAppIdChange:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.queryParams.channelId=-1,e.next=3,c["f"].getOptions({appId:this.queryParams.appId});case 3:t=e.sent,0==t.code&&(this.channelOptions=t.data);case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()}},p=d,m=a("2877"),f=Object(m["a"])(p,r,n,!1,null,null,null);t["default"]=f.exports},f56f:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"app-container"},[a("el-card",[a("div",{attrs:{slot:"header"},slot:"header"},[e._v("清理缓存")]),e._v(" "),a("div",[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px",size:"medium"}},[a("el-row",[a("el-form-item",{attrs:{label:"key",prop:"key"}},[a("el-input",{attrs:{placeholder:"请输入key清理缓存",clearable:""},model:{value:e.form.key,callback:function(t){e.$set(e.form,"key","string"===typeof t?t.trim():t)},expression:"form.key"}}),e._v(" "),a("span",{staticClass:"extra"},[e._v("注意：不需要输入key的前缀；支持批量删除，当key以*结尾时（如xxx*），将删除所有以xxx开头的Key")])],1),e._v(" "),e.historyKeys.length>0?a("div",{staticClass:"history"},[a("span",{staticClass:"text"},[e._v("历史记录：")]),e._v(" "),e._l(e.historyKeys,(function(t,r){return a("el-tag",{key:r,staticClass:"item",attrs:{size:"mini",closable:""},on:{close:function(t){return e.onTagClose(r)},click:function(a){return e.onTagClick(t)}}},[e._v(e._s(t))])}))],2):e._e(),e._v(" "),a("el-form-item",{attrs:{label:" "}},[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("清理缓存")])],1)],1)],1)],1)])],1)},n=[],i=(a("20d6"),a("6762"),a("2fdb"),a("96cf"),a("1da1")),s=a("2b0e"),l=a("365c"),o={data:function(){return{loading:!1,form:{},rules:{key:[{required:!0,message:"请输入key"}]},historyKeys:[]}},mounted:function(){this.reload()},methods:{handleSubmit:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a){var r;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a){t.next=7;break}return e.$xloading.show(),t.next=4,l["m"].deleteByKey(e.form.key);case 4:r=t.sent,e.$xloading.hide(),0==r.code?(e.$xMsgSuccess("删除成功"),e.saveKeyHistory(e.form.key)):e.$xMsgError("删除失败！".concat(r.msg));case 7:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},handleReset:function(){this.form={}},reload:function(){this.historyKeys=this.getHistory()||[]},saveKeyHistory:function(e){if(e){var t=this.getHistory();if(t)if(t.includes(e)){var a=t.findIndex((function(t){return t==e}));t.splice(a,1),t.unshift(e),this.setHistory(t)}else 10==t.length&&t.splice(t.length-1,1),t.unshift(e),this.setHistory(t);else this.setHistory([e])}},setHistory:function(e){s["default"].ls.set("redis_key_history",e),this.historyKeys=e},getHistory:function(){var e=s["default"].ls.get("redis_key_history");return e},onTagClose:function(e){var t=this.getHistory();t.splice(e,1),this.setHistory(t)},onTagClick:function(e){this.$set(this.form,"key",e)}}},c=o,u=(a("00d8c"),a("2877")),d=Object(u["a"])(c,r,n,!1,null,"39dc9adc",null);t["default"]=d.exports},f5e8:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-form",{ref:"queryForm",attrs:{inline:"","label-width":"80px"}},[a("el-row",{attrs:{gutter:15}},[a("el-form-item",{attrs:{label:"登录账号"}},[a("el-input",{model:{value:e.queryParams.loginName,callback:function(t){e.$set(e.queryParams,"loginName",t)},expression:"queryParams.loginName"}})],1),e._v(" "),a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{attrs:{placeholder:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.statusOptions,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),e._v(" "),a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{attrs:{type:"daterange",placeholder:"","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.date,callback:function(t){e.$set(e.queryParams,"date",t)},expression:"queryParams.date"}})],1),e._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:function(t){return e.$refs.table.refresh(!0)}}},[e._v("搜索")]),e._v(" "),a("el-button",{attrs:{icon:"el-icon-refresh"},on:{click:function(){return e.queryParams={}}}},[e._v("重置")])],1)],1)],1),e._v(" "),a("el-row",{ref:"toolbar",staticClass:"table-toolbar"}),e._v(" "),a("x-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"table",attrs:{data:e.tableData,"row-key":"id",loadData:e.getList,height:e.tableHeight}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"logId",label:"ID",align:"center",width:"120"}}),e._v(" "),a("el-table-column",{attrs:{prop:"loginName",label:"登录账号",align:"center",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"realName",label:"姓名",align:"center",width:"150"}}),e._v(" "),a("el-table-column",{attrs:{prop:"ipAddr",label:"登录IP",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[e._v(e._s(t.row.ipAddr))]),e._v(" "),a("div",[e._v(e._s(t.row.ipLocation))])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"status",label:"状态",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"==t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("成功")]):"1"==t.row.status?a("el-tag",{attrs:{type:"warning"}},[e._v("失败")]):a("el-tag",{attrs:{type:"danger"}},[e._v("异常")])]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"msg",label:"操作信息",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"loginTime",label:"登录时间",align:"center",width:"180"}})],1)],1)],1)},n=[],i=(a("96cf"),a("1da1")),s=a("4624"),l=a("a457"),o={mixins:[s["a"]],data:function(){return{queryParams:{},loading:!1,tableData:{},statusOptions:[{label:"成功",value:"0"},{label:"失败",value:"1"}]}},methods:{getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),t=Object.assign({},t,this.queryParams),t.date&&(t.beginTime=t.date[0],t.endTime=t.date[1]),e.next=5,l["a"](t);case 5:a=e.sent,this.tableData=a,this.$xloading.hide();case 8:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}(),renderOperateType:function(e){var t=e.operateType,a="未知";switch(t){case"1":a="新增";break;case"2":a="修改";break;case"3":a="删除";break;case"4":a="查询";break;case"5":a="退出登录";break;default:}return a}}},c=o,u=a("2877"),d=Object(u["a"])(c,r,n,!1,null,null,null);t["default"]=d.exports},f645:function(e,t,a){"use strict";t["a"]={apkDlownloadHost:"https://apkd.cu5668.cn/",customerAdminLoginUrl:"http://yyaccustomer.jjyii.com/adminlogin"}},f794:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-card",[a("el-row",{ref:"toolbar",staticClass:"table-toolbar"},[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:menu:add"],expression:"['system:menu:add']"}],attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.menuList,"row-key":"id","tree-props":{children:"children"}}},[a("el-table-column",{attrs:{prop:"menuName",label:"菜单名称","min-width":"160"}}),e._v(" "),a("el-table-column",{attrs:{prop:"icon",label:"图标","min-width":"60",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){var t=e.row;return[a("x-icon",{attrs:{icon:t.icon}})]}}])}),e._v(" "),a("el-table-column",{attrs:{prop:"sort",label:"排序","min-width":"60",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"component",label:"组件路径","show-overflow-tooltip":!0,"min-width":"200",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"permission",label:"权限标识","show-overflow-tooltip":!0,"min-width":"200",align:"center"}}),e._v(" "),a("el-table-column",{attrs:{prop:"isShow",label:"可见","min-width":"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return["1"==t.row.isShow?a("el-tag",{attrs:{type:"success"}},[e._v("显示")]):a("el-tag",{attrs:{type:"danger"}},[e._v("隐藏")])]}}])}),e._v(" "),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width","min-width":"250"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:menu:edit"],expression:"['system:menu:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleEdit(t.row)}}},[e._v("修改")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:menu:add"],expression:"['system:menu:add']"}],attrs:{size:"mini",type:"text",icon:"el-icon-plus"},on:{click:function(a){return e.handleAdd(t.row)}}},[e._v("新增")]),e._v(" "),a("el-button",{directives:[{name:"permission",rawName:"v-permission",value:["system:menu:delete"],expression:"['system:menu:delete']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1)],1),e._v(" "),a("edit-dialog",{ref:"editDialog",on:{ok:e.handleOk}})],1)},n=[],i=(a("96cf"),a("1da1")),s=a("4624"),l=a("a6dc"),o=a("89ee"),c={mixins:[s["a"]],components:{EditDialog:o["default"]},data:function(){return{tableHeightOptions:{page:!1},queryParams:{},loading:!1,menuList:[]}},created:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:this.getList();case 1:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),methods:{onSystemCodeChange:function(e){this.queryParams.systemCode=e,this.getList()},getList:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return this.$xloading.show(),e.next=3,l["getList"](this.queryParams);case 3:t=e.sent,this.menuList=t.data,this.$xloading.hide();case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),handleOk:function(){this.getList()},handleAdd:function(e){this.$refs.editDialog.add(e)},handleEdit:function(e){this.$refs.editDialog.edit(e)},handleDelete:function(e){var t=this;this.$confirm("是否确认删除该数据项？","提示",{type:"warning"}).then((function(){t.deleteData(e)})).catch((function(){}))},deleteData:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t){var a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,l["delMenu"](t.id);case 2:a=e.sent,0==a.code?(this.$xMsgSuccess("删除成功"),this.getList()):this.x.msgError("删除失败！"+a.msg);case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()}},u=c,d=a("2877"),p=Object(d["a"])(u,r,n,!1,null,null,null);t["default"]=p.exports}}]);