(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5bd3efb7"],{"0c09":function(t,e,a){"use strict";a("f1a5")},"352b":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{class:t.className,style:{height:t.height,width:t.width}})},s=[],i=(a("7f7f"),a("ac6a"),a("c5f6"),a("313e")),r=a("ed08"),l={data:function(){return{$_sidebarElm:null,$_resizeHandler:null}},mounted:function(){var t=this;this.$_resizeHandler=Object(r["debounce"])((function(){t.chart&&t.chart.resize()}),100),this.$_initResizeEvent(),this.$_initSidebarResizeEvent()},beforeDestroy:function(){this.$_destroyResizeEvent(),this.$_destroySidebarResizeEvent()},activated:function(){this.$_initResizeEvent(),this.$_initSidebarResizeEvent()},deactivated:function(){this.$_destroyResizeEvent(),this.$_destroySidebarResizeEvent()},methods:{$_initResizeEvent:function(){window.addEventListener("resize",this.$_resizeHandler)},$_destroyResizeEvent:function(){window.removeEventListener("resize",this.$_resizeHandler)},$_sidebarResizeHandler:function(t){"width"===t.propertyName&&this.$_resizeHandler()},$_initSidebarResizeEvent:function(){this.$_sidebarElm=document.getElementsByClassName("sidebar-container")[0],this.$_sidebarElm&&this.$_sidebarElm.addEventListener("transitionend",this.$_sidebarResizeHandler)},$_destroySidebarResizeEvent:function(){this.$_sidebarElm&&this.$_sidebarElm.removeEventListener("transitionend",this.$_sidebarResizeHandler)}}};a("817d");var c={mixins:[l],props:{className:{type:String,default:"chart"},width:{type:String,default:"100%"},height:{type:String,default:"350px"},titleShow:{type:Boolean,default:!1},titleText:{type:String,default:""},legentShow:{type:Boolean,default:!0},legendX:{type:String,default:"center"},legendY:{type:String,default:"top"},autoResize:{type:Boolean,default:!0},chartData:{type:Object,required:!0},interval:{type:Number,default:59}},data:function(){return{chart:null}},watch:{chartData:{deep:!0,handler:function(t){this.setOptions(t),this.chart.resize()}}},mounted:function(){var t=this;this.$nextTick((function(){t.initChart()}))},beforeDestroy:function(){this.chart&&(this.chart.dispose(),this.chart=null)},methods:{initChart:function(){this.chart=i["init"](this.$el,"macarons"),this.setOptions(this.chartData)},setOptions:function(t){this.chart.setOption({backgroundColor:"",title:{show:this.titleShow,text:this.titleText,left:"center",padding:[5,0,0,15]},xAxis:{splitLine:{show:!1,lineStyle:{color:"#9ea3a6",width:.5,type:"solid"}},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{interval:this.interval,textStyle:{color:"#474444",margin:10},formatter:"{value}"},type:"category",boundaryGap:!1,data:t.columns},yAxis:{splitLine:{show:!0,lineStyle:{color:"#9ea3a6",width:.5}},axisLabel:{textStyle:{color:"#474444",margin:10}},axisLine:{show:!1,lineStyle:{color:"#fff",width:1,type:"solid"}},scale:!0,type:"value"},grid:{left:10,right:10,bottom:30,top:50,containLabel:!0},tooltip:{trigger:"axis",backgroundColor:"rgba(255,255,255,0.8)",axisPointer:{type:"line"},padding:[5,10],formatter:function(t){var e='<span style="color: #000000;font-size:xx-small;">'+t[0].axisValue+"</span><br/>";return t.forEach((function(a,n){null!=a.data&&(e+="".concat(a.marker,'<span style="color: #000000;font-size:x-small;">').concat(a.seriesName,": <b>").concat(a.data,"</b></span>"),e+=n===t.length-1?"":"<br/>")})),e}},legend:{show:this.legentShow,x:this.legendX,y:"bottom",data:t.name,textStyle:{padding:[0,3]},itemWidth:14,itemHeight:10,itemGap:50},series:t.series},!0)}}},o=c,d=a("2877"),h=Object(d["a"])(o,n,s,!1,null,null,null);e["a"]=h.exports},"3e3b":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-row",{staticClass:"panel-group",attrs:{gutter:40}},[a("el-col",{staticClass:"card-panel-col",attrs:{xs:24,sm:24,lg:8}},[a("div",{staticClass:"card-panel",on:{click:function(e){return t.handleSetLineChartData(0)}}},[a("div",{staticClass:"card-panel-icon-wrapper icon-people"},[a("svg-icon",{attrs:{"icon-class":"peoples","class-name":"card-panel-icon"}})],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("今日新增用户")]),t._v(" "),a("font",{staticStyle:{"font-size":"18px"}},[t._v(" "+t._s(t.data.newUserData.value)+" ")]),t._v(" "),a("font",{staticStyle:{"font-size":"14px"}},[t._v("个")])],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("昨日新增用户")]),t._v(" "),a("font",{staticStyle:{"font-size":"18px"}},[t._v(" "+t._s(t.data.newUserData.oldValue)+" ")]),t._v(" "),a("font",{staticStyle:{"font-size":"14px"}},[t._v("个")])],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("同比增长")]),t._v(" "),t.data.newUserData.growthRate<0?a("font",{staticClass:"green",staticStyle:{"font-size":"18px"}},[t._v("\n          "+t._s(t.data.newUserData.growthRate)+"%\n        ")]):t._e(),t._v(" "),t.data.newUserData.growthRate>=0?a("font",{staticClass:"red",staticStyle:{"font-size":"18px"}},[t._v("\n          "+t._s(t.data.newUserData.growthRate)+"%\n        ")]):t._e(),t._v(" "),t.data.newUserData.growthRate<0?a("svg-icon",{staticClass:"green",attrs:{"icon-class":"umicon-decrease"}}):t._e(),t._v(" "),t.data.newUserData.growthRate>=0?a("svg-icon",{staticClass:"red",attrs:{"icon-class":"umicon-increas"}}):t._e()],1)])]),t._v(" "),a("el-col",{staticClass:"card-panel-col",attrs:{xs:24,sm:24,lg:8}},[a("div",{staticClass:"card-panel",on:{click:function(e){return t.handleSetLineChartData(4)}}},[a("div",{staticClass:"card-panel-icon-wrapper icon-people"},[a("svg-icon",{attrs:{"icon-class":"active","class-name":"card-panel-icon"}})],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("今日活跃用户")]),t._v(" "),a("font",{staticStyle:{"font-size":"18px"}},[t._v(" "+t._s(t.data.activeUserData.value)+" ")]),t._v(" "),a("font",{staticStyle:{"font-size":"14px"}},[t._v("个")])],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("昨日活跃用户")]),t._v(" "),a("font",{staticStyle:{"font-size":"18px"}},[t._v(" "+t._s(t.data.activeUserData.oldValue)+" ")]),t._v(" "),a("font",{staticStyle:{"font-size":"14px"}},[t._v("个")])],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("同比增长")]),t._v(" "),t.data.activeUserData.growthRate<0?a("font",{staticClass:"green",staticStyle:{"font-size":"18px"}},[t._v("\n          "+t._s(t.data.activeUserData.growthRate)+"%\n        ")]):t._e(),t._v(" "),t.data.activeUserData.growthRate>=0?a("font",{staticClass:"red",staticStyle:{"font-size":"18px"}},[t._v("\n          "+t._s(t.data.activeUserData.growthRate)+"%\n        ")]):t._e(),t._v(" "),t.data.activeUserData.growthRate<0?a("svg-icon",{staticClass:"green",attrs:{"icon-class":"umicon-decrease"}}):t._e(),t._v(" "),t.data.activeUserData.growthRate>=0?a("svg-icon",{staticClass:"red",attrs:{"icon-class":"umicon-increas"}}):t._e()],1)])]),t._v(" "),a("el-col",{staticClass:"card-panel-col",attrs:{xs:24,sm:24,lg:8}},[a("div",{staticClass:"card-panel",on:{click:function(e){return t.handleSetLineChartData(5)}}},[a("div",{staticClass:"card-panel-icon-wrapper icon-people"},[a("svg-icon",{attrs:{"icon-class":"userremain","class-name":"card-panel-icon"}})],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("昨日留存用户")]),t._v(" "),a("font",{staticStyle:{"font-size":"18px"}},[t._v(" "+t._s(t.data.remainUserData.value)+" ")]),t._v(" "),a("font",{staticStyle:{"font-size":"14px"}},[t._v("个")])],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("前日留存用户")]),t._v(" "),a("font",{staticStyle:{"font-size":"18px"}},[t._v(" "+t._s(t.data.remainUserData.oldValue)+" ")]),t._v(" "),a("font",{staticStyle:{"font-size":"14px"}},[t._v("个")])],1),t._v(" "),a("div",{staticClass:"card-panel-description"},[a("div",{staticClass:"card-panel-text"},[t._v("同比增长")]),t._v(" "),t.data.remainUserData.growthRate<0?a("font",{staticClass:"green",staticStyle:{"font-size":"18px"}},[t._v("\n          "+t._s(t.data.remainUserData.growthRate)+"%\n        ")]):t._e(),t._v(" "),t.data.remainUserData.growthRate>=0?a("font",{staticClass:"red",staticStyle:{"font-size":"18px"}},[t._v("\n          "+t._s(t.data.remainUserData.growthRate)+"%\n        ")]):t._e(),t._v(" "),t.data.remainUserData.growthRate<0?a("svg-icon",{staticClass:"green",attrs:{"icon-class":"umicon-decrease"}}):t._e(),t._v(" "),t.data.remainUserData.growthRate>=0?a("svg-icon",{staticClass:"red",attrs:{"icon-class":"umicon-increas"}}):t._e()],1)])])],1)},s=[],i={components:{},props:{data:{type:Object,required:!0}},data:function(){return{}},methods:{handleSetLineChartData:function(t){this.$emit("handleSetLineChartData",t)}}},r=i,l=(a("0c09"),a("2877")),c=Object(l["a"])(r,n,s,!1,null,"5fb5fe67",null);e["default"]=c.exports},"4f73":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-card",[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("新增前10渠道")])]),t._v(" "),a("el-row",[a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{type:"index",label:"序号",align:"center",width:"50"}}),t._v(" "),a("el-table-column",{attrs:{prop:"date",label:"日期",align:"center",width:"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"appName",label:"应用名称",align:"center","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"channelName",label:"渠道名称",align:"center","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"install",label:"新增用户数",align:"center","min-width":"100"}}),t._v(" "),a("el-table-column",{attrs:{prop:"launchUserOld",label:"留存用户数",align:"center","min-width":"150"}}),t._v(" "),a("el-table-column",{attrs:{prop:"activeUserCount",label:"活跃用户数",align:"center","min-width":"150"}})],1)],1)],1)},s=[],i={components:{},props:{tableData:{type:Array,required:!0}},data:function(){return{}},methods:{}},r=i,l=a("2877"),c=Object(l["a"])(r,n,s,!1,null,null,null);e["default"]=c.exports},9406:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("panel-group",{attrs:{data:t.dashboardData},on:{handleSetLineChartData:t.handleSetLineChartData}}),t._v(" "),a("el-row",{staticStyle:{background:"#fff",padding:"16px 16px 0","margin-bottom":"10px"}},[a("x-radio",{staticStyle:{"margin-bottom":"10px","padding-left":"10px"},attrs:{size:"small","button-style":"solid",button:"",url:"/options/GetTimeSpanTypes"},on:{change:function(e){return t.handleSetLineChartData()}},model:{value:t.queryParams.timeSpanType,callback:function(e){t.$set(t.queryParams,"timeSpanType",e)},expression:"queryParams.timeSpanType"}}),t._v(" "),a("line-chart",{attrs:{"chart-data":t.dashboardLineData,legentShow:!0,titleShow:!0,height:"300px",titleText:t.dashboardLineData.title,interval:t.dashboardLineData.interval}})],1),t._v(" "),a("panel-table",{attrs:{tableData:t.dashboardTableData}})],1)},s=[],i=(a("96cf"),a("1da1")),r=a("3e3b"),l=a("4f73"),c=a("352b"),o=a("365c"),d={name:"DashboardAdmin",components:{PanelGroup:r["default"],LineChart:c["a"],PanelTable:l["default"]},data:function(){return{queryParams:{appFlag:-1,channelId:-1,analysisType:0,timeSpanType:5},dashboardData:{},dashboardLineData:{},dashboardTableData:[]}},created:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.$xloading.show(),t.next=3,this.getDatas();case 3:return t.next=5,this.getLineDatas(0);case 5:return t.next=7,this.getChannelStats();case 7:this.$xloading.hide();case 8:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),methods:{getDatas:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,o["h"].getDashboard();case 2:e=t.sent,0==e.code&&(this.dashboardData=e.data||{});case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getChannelStats:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(){var e;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,o["h"].getChanelStats();case 2:e=t.sent,0==e.code&&(this.dashboardTableData=e.data||{});case 4:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}(),getLineDatas:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(e){var a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=Object.assign({},this.queryParams),a.analysisType=e,t.next=4,o["a"].getinstalllinedata(a);case 4:n=t.sent,0==n.code&&(this.dashboardLineData=n.data||{});case 6:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}(),handleSetLineChartData:function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return this.$xloading.show(),t.next=3,this.getLineDatas(e);case 3:this.$xloading.hide();case 4:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()}},h=d,p=a("2877"),u=Object(p["a"])(h,n,s,!1,null,null,null);e["default"]=u.exports},f1a5:function(t,e,a){}}]);