<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <el-col :span="24" v-if="isEdit">
          <el-form-item label="项目凭证" prop="appKey">
            <el-input v-model="form.appKey" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="版本号" prop="verCode">
            <el-input-number v-model="form.verCode" placeholder="请输入版本号" style="width: 35%" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="标题" prop="title">
            <el-input v-model="form.title" placeholder="请输入标题" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="内容" prop="content">
            <el-input
              type="textarea"
              rows="6"
              v-model="form.content"
              placeholder="请输入升级内容说明..."
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="升级权重" prop="upgradeWeight">
            <el-input-number
              v-model="form.upgradeWeight"
              placeholder="请输入升级权重"
              value="100"
              style="width: 35%"
            />
            <span class="extra">1~100，每次请求升级的概率</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="" prop="enable">
            <el-checkbox v-model="form.enable">开启升级</el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="" prop="force">
            <el-checkbox v-model="form.force">强制更新</el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="" prop="isTest">
            <el-checkbox v-model="form.isTest">启用测试</el-checkbox>
            <span class="extra">勾选后，测试用户Id才可以收到升级信息</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="" prop="isJump">
            <el-checkbox v-model="form.isJump">启用跳转</el-checkbox>
            <span class="extra" style="color: red">勾选后，App打开时候将自动跳转</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="跳转目标包名" prop="jumpToPackName">
            <el-input v-model="form.jumpToPackName" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="针对版本升级" prop="targetVer">
            <el-input v-model="form.targetVer" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="针对用户升级" prop="targetUids">
            <el-input
              type="textarea"
              rows="3"
              v-model="form.targetUids"
              placeholder="请输入用户升级Id..."
            />
            <span class="extra">用户升级Id，可填多个，用,分隔</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="测试用户ID" prop="testUids">
            <el-input
              type="textarea"
              rows="3"
              v-model="form.testUids"
              placeholder="请输入测试用户Id..."
            />
            <span class="extra">测试用户Id，可填多个，用,分隔</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="上传安装包" prop="packURL">
            <x-upload
              v-model="form.packURL"
              :upload-data="uploadData"
              list-type="text"
              drag
              @success="onUploadSuccess"
            ></x-upload>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.packURL_Url">
          <el-form-item label="安装包下载链接">
            <a :href="form.packURL_Url">{{ form.packURL_Url }}</a>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.packURL_Url" prop="packMD5">
          <el-form-item label="安装包MD5">
            <el-input v-model="form.packMD5" placeholder="" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import XUpload from '@/components/XUpload'
import { appUpgradeConfigApi } from '@/api'
import { UPLOAD_TYPE } from '@/utils/constant'
import { mixinDevice } from '@/utils/mixin'

export default {
  mixins: [mixinDevice],
  components: {
    XUpload,
  },
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
      rules: {
        appKey: [{ required: true, message: '项目凭证不能为空', trigger: 'blur' }],
        verCode: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
        title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
        packPath: [{ required: true, message: '安装包不能为空', trigger: 'blur' }],
      },
      keyConfig: {},
      uploadData: {
        uploadType: UPLOAD_TYPE.appUpgradeApk,
      },
      isEdit: false,
    }
  },
  methods: {
    add(keyConfig) {
      this.isEdit = false
      this.keyConfig = keyConfig
      this.uploadData.appFlag = keyConfig.appFlag
      this.form.appKey = keyConfig.appKey
      this.reset()
      this.title = `添加【${keyConfig.appName}】升级包`
      this.visible = true
    },
    async edit(row, keyConfig) {
      this.isEdit = true
      this.keyConfig = keyConfig
      this.uploadData.appFlag = keyConfig.appFlag
      this.form.appKey = keyConfig.appKey
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await appUpgradeConfigApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        // return
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          data.appKey = this.keyConfig.appKey
          const { id } = data
          if (id != undefined) {
            const res = await appUpgradeConfigApi.editAppUpgradeConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await appUpgradeConfigApi.addAppUpgradeConfig(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },

    onUploadSuccess({ data }) {
      console.log('upload success: ', data)
      this.form.packMD5 = data.md5
      this.form.packURL_Url = data.url
    },

    download(downloadUrl) {
      this.$router.push(downloadUrl)
    },
  },
}
</script>

<style></style>
