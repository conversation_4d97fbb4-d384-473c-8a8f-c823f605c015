<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="所属项目">
          <x-select
            show-default
            customRender
            v-model="queryParams.appId"
            url="/appKeyConfig/options"
          ></x-select>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['channel:add']"
          >新增</el-button
        >
        <el-button
          size="mini"
          type="warning"
          icon="el-icon-edit"
          :disabled="selectedRows.length === 0"
          @click="handleBatchEdit"
          v-permission="['channel:edit']"
          >批量修改</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" align="center" width="55"></el-table-column>
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="渠道Id" align="center" width="100"></el-table-column>
        <el-table-column
          prop="appName"
          label="项目名称"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="渠道名称"
          align="center"
          min-width="200"
        ></el-table-column>
        <!--<el-table-column
          prop="channelKey"
          label="渠道标识"
          align="center"
          min-width="280"
        ></el-table-column>
         -<el-table-column prop="latestPkgMd5" label="渠道包MD5" align="center" min-width="220">
          <template v-slot="{ row }">
            <span v-if="row.latestPkgMd5">{{ row.latestPkgMd5 }}</span>
            <el-tag v-else size="mini" type="danger">未上传渠道包</el-tag>
          </template>
        </el-table-column>-->
        <el-table-column prop="enable" label="可用" align="center" width="100">
          <template slot-scope="{ row }">
            <el-tag type="success" size="medium" v-if="row.enable">是</el-tag>
            <el-tag type="danger" size="medium" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isDefault" label="默认渠道" align="center" width="100">
          <template slot-scope="{ row }">
            <el-tag type="success" size="medium" v-if="row.isDefault">是</el-tag>
            <el-tag type="danger" size="medium" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="createOn"
          label="添加时间"
          align="center"
          width="180"
        ></el-table-column>
        <el-table-column label="操作" min-width="250" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="info"
              @click="handleDownloadApk(scope.row)"
              v-permission="['channel:downloadApk']"
              >下载</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['channel:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['channel:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
    <download-dialog ref="downloadDialog" />
    <batch-edit-dialog ref="batchEditDialog" @ok="handleOk" />
  </div>
</template>

<script>
import config from '@/config/app.config'
import { tableHeightMixin } from '@/mixin'
import { channelApi } from '@/api'
import EditDialog from './Edit'
import DownloadDialog from './DownloadDialog'
import BatchEditDialog from './BatchEdit'
export default {
  components: {
    EditDialog,
    DownloadDialog,
    BatchEditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
      apkDlownloadHost: config.apkDlownloadHost,
      selectedRows: [], // 选中的行数据
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await channelApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await channelApi.delChannel(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection.filter((row) => row.isDefault == false) // 过滤掉默认渠道，并返回新数组
    },

    // 批量修改
    handleBatchEdit() {
      if (this.selectedRows.length === 0) {
        this.$xMsgWarning('请先选择要修改的数据')
        return
      }
      this.$refs.batchEditDialog.open(this.selectedRows)
    },

    async handleDownloadApk(row) {
      var proj = row.appFlag
      switch (row.appFlag) {
        case 'kkz':
          proj = 'gamebox'
          break
      }
      var url = this.apkDlownloadHost + '?p=' + proj + '&cid=' + row.id
      var dataExt = { downloadUrl: url }
      this.$refs.downloadDialog.show(row, dataExt)
    },
  },
}
</script>
