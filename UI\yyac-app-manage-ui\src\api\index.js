import * as loginApi from '@/api/system/login'
import * as menuApi from '@/api/system/menu'
import * as roleApi from '@/api/system/role'
import * as sysUserApi from '@/api/system/user'
import * as authApi from '@/api/system/auth'
import * as redisApi from '@/api/system/redis'
import * as systemNoticeApi from '@/api/system/notice'

import * as fileApi from '@/api/file'
import * as optionsApi from '@/api/options'

import * as appKeyConfigApi from '@/api/config/appkey-config'
import * as symbolConfigApi from '@/api/config/symbol-config'

import * as channelApi from '@/api/channel/channel'
import * as channeluserApi from '@/api/channel/channeluser'

import * as statsApi from '@/api/stats/stats'
import * as analysisApi from '@/api/stats/analysis'
import * as dashboardApi from '@/api/stats/dashboard'

import * as appRunLogApi from '@/api/logs/apprunlog'
import * as symbolRunLog<PERSON>pi from '@/api/logs/symbolrunlog'

import * as appUpgradeConfigApi from '@/api/upgrade/upgrade-config'

export {
  loginApi,
  menuApi,
  roleApi,
  sysUserApi,
  authApi,
  redisApi,
  systemNoticeApi,
  fileApi,
  optionsApi,
  appKeyConfigApi,
  symbolConfigApi,
  channelApi,
  statsApi,
  analysisApi,
  dashboardApi,
  appRunLogApi,
  symbolRunLogApi,
  channeluserApi,
  appUpgradeConfigApi,
}
