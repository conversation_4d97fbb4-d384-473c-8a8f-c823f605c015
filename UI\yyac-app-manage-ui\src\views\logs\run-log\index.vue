<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="date"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="所属项目">
          <x-select
            show-default
            customRender
            v-model="queryParams.appFlag"
            url="/appKeyConfig/flagOptions"
          ></x-select>
        </el-form-item>
        <el-form-item label="启动类型">
          <x-select show-default v-model="queryParams.runType" :options="runTypeOptions"></x-select>
        </el-form-item>
        <el-form-item label="安卓Id">
          <el-input v-model="queryParams.deviceId"></el-input>
        </el-form-item>
        <el-form-item label="渠道Id">
          <el-input v-model="queryParams.channelId"></el-input>
        </el-form-item>
        <el-form-item label="包渠道Id">
          <el-input v-model="queryParams.packCId"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="runType" label="启动类型" align="center" width="100">
          <template slot-scope="{ row }">
            <el-tag size="mini" v-if="row.runType == 0">安装</el-tag>
            <el-tag size="mini" v-else-if="row.runType == 1">启动</el-tag>
            <el-tag size="mini" v-else-if="row.runType == 2">退出</el-tag>
            <el-tag size="mini" v-else-if="row.runType == 3">卸载</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="channelId"
          label="渠道Id"
          align="center"
          width="100"
        ></el-table-column>
        <el-table-column
          prop="packCId"
          label="包渠道Id"
          align="center"
          width="100"
        ></el-table-column>
        <el-table-column
          prop="channelName"
          label="渠道名称"
          align="center"
          width="200"
        ></el-table-column>
        <el-table-column
          prop="appFlag"
          label="App标识"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="pkgName"
          label="包名"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column prop="ver" label="Ver" align="center" min-width="100"></el-table-column>
        <el-table-column
          prop="verCode"
          label="版本号"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="deviceId"
          label="安卓Id"
          align="center"
          min-width="200"
        ></el-table-column>
        <el-table-column prop="ip" label="Ip" align="center" min-width="150"></el-table-column>
        <el-table-column prop="brand" label="品牌" align="center" min-width="100"></el-table-column>
        <el-table-column prop="model" label="型号" align="center" min-width="200"></el-table-column>
        <el-table-column
          prop="screen"
          label="屏幕"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="createOn" label="时间" align="center" width="180"></el-table-column>
      </x-table>
    </el-card>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { tableHeightMixin } from '@/mixin'
import { appRunLogApi } from '@/api'
export default {
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {
        appFlag: -1,
        runType: -1,
        date: '',
      },
      loading: false,
      tableData: {},
      runTypeOptions: [
        { label: '安装', value: 0 },
        { label: '启动', value: 1 },
        { label: '退出', value: 2 },
      ],
    }
  },
  created() {
    this.reset()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        appFlag: -1,
        runType: -1,
        date: dayjs().format('YYYY-MM-DD'),
      }
      this.$refs.table.refresh(true)
    },
    reset() {
      const beginDate = dayjs().format('YYYY-MM-DD')
      this.queryParams.date = beginDate
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date
      }
      const res = await appRunLogApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await appRunLogApi.delAppRunLog(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },
  },
}
</script>
