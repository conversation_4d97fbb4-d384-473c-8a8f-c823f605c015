<template>
  <el-card>
    <div slot="header" class="clearfix">
      <span>新增前10渠道</span>
    </div>
    <el-row>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column prop="date" label="日期" align="center" width="150"></el-table-column>
        <el-table-column
          prop="appName"
          label="应用名称"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="channelName"
          label="渠道名称"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column
          prop="install"
          label="新增用户数"
          align="center"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="launchUserOld"
          label="留存用户数"
          align="center"
          min-width="150"
        ></el-table-column>
        <el-table-column prop="activeUserCount" label="活跃用户数" align="center" min-width="150">
        </el-table-column>
      </el-table>
    </el-row>
  </el-card>
</template>

<script>
export default {
  components: {
    //CountTo
  },
  props: {
    tableData: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {}
  },
  methods: {},
}
</script>
