<template>
  <x-dialog
    title="批量修改渠道"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <div class="batch-edit-content">
      <!-- 选中的渠道信息 -->
      <div class="selected-info">
        <h4>已选择 {{ selectedChannels.length }} 个渠道：</h4>
        <div class="selected-list">
          <el-tag
            v-for="channel in selectedChannels"
            :key="channel.id"
            size="small"
            style="margin: 2px"
          >
            {{ channel.appName }} - {{ channel.name }}
          </el-tag>
        </div>
      </div>

      <!-- 修改表单 -->
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
        <el-form-item label="所属项目" prop="appId">
          <x-select customRender v-model="form.appId" url="/appKeyConfig/options"></x-select>
        </el-form-item>
      </el-form>
    </div>
  </x-dialog>
</template>

<script>
import { channelApi } from '@/api'

export default {
  data() {
    return {
      visible: false,
      loading: false,
      selectedChannels: [],
      form: {
        appId: '',
      },
      rules: {
        appId: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
      },
    }
  },
  methods: {
    open(selectedRows) {
      this.selectedChannels = selectedRows
      this.reset()
      this.visible = true
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {
        appId: '',
      }
      this.$xResetForm('form')
    },

    async submitForm() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            // 获取选中渠道的ID列表
            const channelIds = this.selectedChannels.map((channel) => channel.id)

            this.$confirm('是否确认要批量修改所属项目？', '提示', {
              type: 'warning',
            })
              .then(async () => {
                this.$xloading.show()

                // 调用批量修改API
                const res = await channelApi.batchEditChannel({
                  ids: channelIds,
                  appId: this.form.appId,
                })

                this.$xloading.hide()

                if (res.code == 0) {
                  this.$xMsgSuccess('批量修改成功')
                  this.close()
                  this.$emit('ok')
                } else {
                  this.$xMsgError('批量修改失败！' + res.msg)
                }
              })
              .catch((error) => {
                this.$xloading.hide()
                this.$xMsgError('批量修改失败！' + error.message)
              })
          } catch (error) {
            this.$xloading.hide()
          }
        }
      })
    },
  },
}
</script>

<style scoped>
.batch-edit-content {
  padding: 10px 0;
}

.selected-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.selected-info h4 {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.selected-list {
  max-height: 120px;
  overflow-y: auto;
}
</style>
