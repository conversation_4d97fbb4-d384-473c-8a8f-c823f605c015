<template>
  <div class="app-container">
    <el-card>
      <el-form ref="queryForm" inline size="mini">
        <el-form-item label="所属项目">
          <x-select
            show-default
            customRender
            v-model="queryParams.appFlag"
            url="/appKeyConfig/options"
          ></x-select>
        </el-form-item>
        <el-form-item label="渠道">
          <x-select
            show-default
            goroup
            v-model="queryParams.channelId"
            :options="channelOptions"
            url="/channel/options"
          />
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="queryParams.date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="reload"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row>
        <el-card>
          <el-col :span="24">
            <x-radio
              style="margin-bottom: 10px; padding-left: 10px"
              size="small"
              button-style="solid"
              v-model="queryParams.analysisType"
              button
              url="/options/GetAnalysisTypes"
              @change="getLineData()"
            ></x-radio>
            <x-radio
              style="margin-bottom: 10px; padding-left: 10px"
              size="small"
              button-style="solid"
              v-model="queryParams.timeSpanType"
              button
              url="/options/GetTimeSpanTypes"
              @change="getLineData()"
            ></x-radio>
            <line-chart
              :chart-data="installLineData"
              :legentShow="true"
              :titleShow="true"
              :titleText="installLineData.title"
              :interval="installLineData.interval"
            />
          </el-col>
        </el-card>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { tableHeightMixin } from '@/mixin'
import { analysisApi, channelApi } from '@/api'
import LineChart from '@/components/EChatrs/Line/LineChart'

export default {
  components: { LineChart },
  mixins: [tableHeightMixin],
  data() {
    return {
      tableHeightOptions: {
        page: false,
      },
      queryParams: {
        appFlag: -1,
        channelId: -1,
        analysisType: 0,
        timeSpanType: 5,
        date: [],
      },
      loading: false,
      channelOptions: [],
      installLineData: {},
    }
  },
  mounted() {
    this.reset()
    this.getLineData()
  },
  methods: {
    queryReset() {
      this.queryParams = {
        appFlag: -1,
        channelId: -1,
        analysisType: 0,
        timeSpanType: 5,
        date: [],
      }
      this.reset()
      this.getLineData()
    },
    async getLineData() {
      this.$xloading.show()
      var params = Object.assign({}, this.queryParams)
      if (this.queryParams.date) {
        params.beginTime = this.queryParams.date[0]
        params.endTime = this.queryParams.date[1]
      }
      const res = await analysisApi.getinstalllinedata(params)
      console.log('data', res)
      if (res.code == 0) {
        console.log('res', res)
        this.installLineData = res.data || {}
        console.log('installLineData:', this.installLineData)
      }
      this.$xloading.hide()
    },
    reset() {
      const beginDate = dayjs().subtract(1, 'day').format('YYYY-MM-DD')
      const endDate = dayjs().format('YYYY-MM-DD')
      this.queryParams.date = [beginDate, endDate]
    },
    reload() {
      this.getLineData()
    },
    async handleAppIdChange() {
      this.queryParams.channelId = -1
      const res = await channelApi.getOptions({ appId: this.queryParams.appFlag })
      if (res.code == 0) {
        this.channelOptions = res.data
      }
    },
  },
}
</script>

<style></style>
