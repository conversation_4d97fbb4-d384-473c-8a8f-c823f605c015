<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="24" :sm="24" :lg="8" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData(0)">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="peoples" class-name="card-panel-icon" />
        </div>

        <div class="card-panel-description">
          <div class="card-panel-text">今日新增用户</div>
          <font style="font-size: 18px"> {{ data.newUserData.value }} </font>
          <font style="font-size: 14px">个</font>
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">昨日新增用户</div>
          <font style="font-size: 18px"> {{ data.newUserData.oldValue }} </font>
          <font style="font-size: 14px">个</font>
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">同比增长</div>
          <font style="font-size: 18px" v-if="data.newUserData.growthRate < 0" class="green">
            {{ data.newUserData.growthRate }}%
          </font>
          <font style="font-size: 18px" v-if="data.newUserData.growthRate >= 0" class="red">
            {{ data.newUserData.growthRate }}%
          </font>
          <svg-icon
            v-if="data.newUserData.growthRate < 0"
            icon-class="umicon-decrease"
            class="green"
          />
          <svg-icon
            v-if="data.newUserData.growthRate >= 0"
            icon-class="umicon-increas"
            class="red"
          />
        </div>
      </div>
    </el-col>
    <el-col :xs="24" :sm="24" :lg="8" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData(4)">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="active" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">今日活跃用户</div>
          <font style="font-size: 18px"> {{ data.activeUserData.value }} </font>
          <font style="font-size: 14px">个</font>
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">昨日活跃用户</div>
          <font style="font-size: 18px"> {{ data.activeUserData.oldValue }} </font>
          <font style="font-size: 14px">个</font>
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">同比增长</div>
          <font style="font-size: 18px" v-if="data.activeUserData.growthRate < 0" class="green">
            {{ data.activeUserData.growthRate }}%
          </font>
          <font style="font-size: 18px" v-if="data.activeUserData.growthRate >= 0" class="red">
            {{ data.activeUserData.growthRate }}%
          </font>
          <svg-icon
            v-if="data.activeUserData.growthRate < 0"
            icon-class="umicon-decrease"
            class="green"
          />
          <svg-icon
            v-if="data.activeUserData.growthRate >= 0"
            icon-class="umicon-increas"
            class="red"
          />
        </div>
      </div>
    </el-col>
    <el-col :xs="24" :sm="24" :lg="8" class="card-panel-col">
      <div class="card-panel" @click="handleSetLineChartData(5)">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="userremain" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">昨日留存用户</div>
          <font style="font-size: 18px"> {{ data.remainUserData.value }} </font>
          <font style="font-size: 14px">个</font>
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">前日留存用户</div>
          <font style="font-size: 18px"> {{ data.remainUserData.oldValue }} </font>
          <font style="font-size: 14px">个</font>
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">同比增长</div>
          <font style="font-size: 18px" v-if="data.remainUserData.growthRate < 0" class="green">
            {{ data.remainUserData.growthRate }}%
          </font>
          <font style="font-size: 18px" v-if="data.remainUserData.growthRate >= 0" class="red">
            {{ data.remainUserData.growthRate }}%
          </font>
          <svg-icon
            v-if="data.remainUserData.growthRate < 0"
            icon-class="umicon-decrease"
            class="green"
          />
          <svg-icon
            v-if="data.remainUserData.growthRate >= 0"
            icon-class="umicon-increas"
            class="red"
          />
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
export default {
  components: {
    //CountTo
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {}
  },
  methods: {
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    },
  },
}
</script>

<style lang="scss" scoped>
.red {
  color: #ff7474;
}
.green {
  color: #44ca5e;
}
.pink {
  color: hotpink;
}
.panel-group {
  .card-panel-col {
    margin-bottom: 10px;
  }

  .card-panel {
    height: 218;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3;
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      float: left;
      font-weight: bold;
      margin: 26px;
      margin-left: 10px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 15px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width: 550px) {
  .card-panel-description {
    display: block;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
