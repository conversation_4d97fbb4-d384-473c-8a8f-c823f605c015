<template>
  <x-dialog
    :title="title"
    :visible.sync="visible"
    v-loading="loading"
    @submit="submitForm"
    @cancel="close"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="medium">
      <el-row>
        <!-- <el-col :span="24">
          <el-form-item label="Id" prop="id">
            <el-input v-model="form.id" placeholder="请输入Id" />
          </el-form-item>
        </el-col> -->
        <el-col :span="24">
          <el-form-item label="渠道名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入渠道名称" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="所属项目" prop="appId">
            <x-select customRender v-model="form.appId" url="/appKeyConfig/options"></x-select>
            <!-- <el-input v-model="form.appFlag" placeholder="请输入项目标识，只允许填写英文字母" /> -->
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否可用" prop="enable">
            <el-radio-group v-model="form.enable">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="是否默认" prop="isDefault">
            <el-radio-group v-model="form.isDefault">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
            <span class="extra">没有渠道的ID的数据会统计入默认渠道中</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </x-dialog>
</template>

<script>
import { channelApi } from '@/api'
export default {
  data() {
    return {
      title: '',
      visible: false,
      loading: false,
      form: {},
      rules: {
        name: [{ required: true, message: '请输入渠道名称', trigger: 'blur' }],
        projectId: [{ required: true, message: '请选择所属项目', trigger: 'blur' }],
        enable: [{ required: true, message: '请选择是否可用', trigger: 'blur' }],
        isActive: [{ required: true, message: '请选择是否线上项目', trigger: 'blur' }],
        isDefault: [{ required: true, message: '请选择是否默认渠道', trigger: 'blur' }],
      },
    }
  },
  methods: {
    add() {
      this.reset()
      this.title = '新增'
      this.visible = true
    },
    async edit(row) {
      this.reset()
      this.title = '修改'
      this.visible = true
      this.$xloading.show()
      const res = await channelApi.getDetail(row.id)
      if (res.code == 0) {
        this.form = res.data
      }
      this.$xloading.hide()
    },

    close() {
      this.reset()
      this.visible = false
    },

    reset() {
      this.form = {}
      this.$xResetForm('form')
    },

    submitForm() {
      this.$refs['form'].validate(async (valid) => {
        // console.log('submitForm valid: ', valid, 'data: ', this.form)
        if (valid) {
          this.$xloading.show()
          const data = Object.assign({}, this.form)
          const { id } = data
          if (id != undefined) {
            const res = await channelApi.editChannel(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('修改成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          } else {
            const res = await channelApi.addChannel(data)
            this.$xloading.hide()
            if (res.code == 0) {
              this.$xMsgSuccess('添加成功')
              this.close()
              this.$emit('ok')
            } else {
              this.$xMsgError('操作失败！' + res.msg)
            }
          }
        }
      })
    },
  },
}
</script>

<style></style>
