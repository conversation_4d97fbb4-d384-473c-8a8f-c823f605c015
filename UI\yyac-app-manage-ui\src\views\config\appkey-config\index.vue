<template>
  <div class="app-container">
    <el-card>
      <!-- 按钮、搜索 -->
      <!-- <el-form ref="queryForm" label-width="80px" inline size="mini">
        <el-form-item label="自增ID">
          <el-input v-model="queryParams.id"></el-input>
        </el-form-item>
        <el-form-item label="App名称">
          <el-input v-model="queryParams.appName"></el-input>
        </el-form-item>
        <el-form-item label="项目凭证">
          <el-input v-model="queryParams.appKey"></el-input>
        </el-form-item>
        <el-form-item label="项目标识">
          <el-input v-model="queryParams.appFlag"></el-input>
        </el-form-item>
        <el-form-item label="添加时间">
          <el-input v-model="queryParams.createOn"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            size="mini"
            type="primary"
            icon="el-icon-search"
            @click="$refs.table.refresh(true)"
            >搜索</el-button
          >
          <el-button size="mini" icon="el-icon-refresh" @click="queryReset">重置</el-button>
        </el-form-item>
      </el-form> -->

      <el-row ref="toolbar" class="table-toolbar">
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAdd"
          v-permission="['appKeyConfig:add']"
          >添加项目</el-button
        >
      </el-row>

      <!-- 表格 -->
      <x-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        row-key="id"
        :loadData="getList"
        :height="tableHeight"
      >
        <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
        <el-table-column
          prop="appName"
          label="项目名称"
          align="center"
          width="200"
        ></el-table-column>
        <el-table-column
          prop="appFlag"
          label="项目标识"
          align="center"
          width="150"
        ></el-table-column>
        <el-table-column
          prop="dsProjectType"
          title="DS标识"
          align="center"
          :width="100"
        ></el-table-column>
        <el-table-column
          prop="appPkgName"
          label="App包名"
          align="center"
          width="300"
        ></el-table-column>
        <el-table-column prop="isActive" label="线上项目" align="center" width="100">
          <template slot-scope="{ row }">
            <el-tag type="success" size="medium" v-if="row.isActive">是</el-tag>
            <el-tag type="danger" size="medium" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isUpgrade" label="开启升级" align="center" width="100">
          <template slot-scope="{ row }">
            <el-tag type="success" size="medium" v-if="row.isUpgrade">是</el-tag>
            <el-tag type="danger" size="medium" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="appKey"
          label="项目凭证"
          align="center"
          min-width="300"
        ></el-table-column>
        <el-table-column
          prop="createOn"
          label="添加时间"
          align="center"
          width="160"
        ></el-table-column>
        <el-table-column label="操作" min-width="280" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpgradeList(scope.row)"
              v-permission="['appUpgradeConfig:list']"
              >升级列表</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="handleEdit(scope.row)"
              v-permission="['appKeyConfig:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="['appKeyConfig:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </x-table>
    </el-card>

    <edit-dialog ref="editDialog" @ok="handleOk" />
  </div>
</template>

<script>
import { tableHeightMixin } from '@/mixin'
import { appKeyConfigApi } from '@/api'
import EditDialog from './Edit'
export default {
  components: {
    EditDialog,
  },
  mixins: [tableHeightMixin],
  data() {
    return {
      queryParams: {},
      loading: false,
      tableData: {},
    }
  },
  methods: {
    queryReset() {
      this.queryParams = {}
      this.$refs.table.refresh(true)
    },
    async getList(params) {
      this.$xloading.show()
      params = Object.assign({}, params, this.queryParams)
      const res = await appKeyConfigApi.getList(params)
      this.tableData = res
      this.$xloading.hide()
    },
    handleAdd() {
      this.$refs.editDialog.add()
    },
    handleEdit(row) {
      this.$refs.editDialog.edit(row)
    },
    async handleDelete(row) {
      this.$confirm('是否确认删除该数据项？', '提示', {
        type: 'warning',
      })
        .then(async () => {
          this.$xloading.show()
          const res = await appKeyConfigApi.delAppKeyConfig(row.id)
          this.$xloading.hide()
          if (res.code == 0) {
            this.$refs.table.refresh()
            this.$xMsgSuccess('删除成功')
          } else {
            this.$xMsgError('删除失败！' + res.msg)
          }
        })
        .catch(() => {
          this.$xloading.hide()
        })
    },

    handleOk() {
      this.$refs.table.refresh()
    },

    handleUpgradeList(row) {
      this.$router.push({ path: '/upgradeConfig', query: { appKey: row.appKey } })
    },
  },
}
</script>
